
## [v2.0.4] - 2025-07-21
- fix(config): add missing config files in Debug build directory

## [v2.0.3] - 2025-07-08
- docs(config): readd  config files in MinSizeRel to git

## [v2.0.2] - 2025-07-08
- docs(config): add config to git

## [v2.0.1] - 2025-05-09
- fix(debug): fix execute program pwd in launch.json
- fix(log): fix date repeats problem in output messagge

## [v2.0.0] - 2025-01-04

- feat(mes): update mes connect info(BREAKING CHANGE)

## [v1.3.4] - 2024-06-19

+ refactor: sensor切换模式需要先解锁

Manual_enter_mes_data V1.3.3_2024.5.15
- feature: 增加D6调节

Manual enter mes data V1.3.2_2024.4.19
- fix: 光斑判定异常
- refactor: 增加结果PASS NG显示

Manual enter mes data V1.3.1_2024.3.23
- fix: enter false param within faculaTest function

Manual enter mes data V1.3.0_2024.3.20
- refactor: domain 由外部配置
- refactor: MES_SUPER_USER 名称改变, "admin"->"Uadmin"

Manual enter mes data V1.2.0_2023.12.16
- refactor: 调节len adjust结构
- refactor: 调整table ui显示部分

V1.1.0 2023_8_3
- fix: 子线程共享参数调用冲突问题
- fix: sensor异常模式ack导致解析异常
- refactor: 增加本地事务号存储

V1.0.6 2023_8_3
- fix: 事务号索引，全部数据索引未负值问题
- style: 版本号1.0.1

V1.0.5 2023_6_12
- style: 版本号解析修改
- style: 改版本号为1.0.0

V1.0.4 2023_6_9
- fix: 结果正常时，rsn_code 错误码不能写入 0

V1.0.3 2023_6_8
- fix: greymap显示BUG
- fix: 错误原因过长，mes写入BUG
- fix: 工序和userid 丢失BUG

V1.0.2 2023_6_7
- feature: 增加结果可选（直接pass或自动判定）
- feature: 增加 greymap显示

V1.0.1 2023_6_6
- fix: 修复灰度数据接收 BUG

V1.0.0 2023_6_5
- feature: 增加数据录入功能

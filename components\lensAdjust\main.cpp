#include "ConfigInitializer.h"
#include "ConfigService.h"
#include "DynamicConfigManager.h"
#include "clensadjust.h"

// 强制引用配置类型以确保自动注册
#include "../../components/configModule/SystemConfigData.h"
#include "../../sensor/photonSensor/config/FaculaConfigData.h"
#include "config/AdjustProcessConfigData.h"


#include <QApplication>
#include <QMessageBox>
#include <QStringList>
#include <memory>

#include "qLog.h"


int main(int argc, char *argv[]) {
    QApplication a(argc, argv);

    // 初始化日志系统
    MyLogger::QLog::init();
    MyLogger::QLog::installMessageHandler();

    LOG_INFO(MyLogger::LogType::INIT, "lenAdjust application starting...");

    // 强制引用配置类型以确保自动注册（必须在配置初始化之前）
    // 这些引用会触发静态对象的构造，从而执行REGISTER_CONFIG_TYPE宏
    // 创建临时对象以强制链接器包含这些类型
    {
        auto systemConfig = std::make_unique<Config::SystemConfigData>();
        auto faculaConfig = std::make_unique<PhotonSensor::FaculaConfigData>();
        auto adjustConfig = std::make_unique<LensAdjust::AdjustProcessConfigData>();

        // 强制引用静态方法
        (void)systemConfig->staticTypeName();
        (void)faculaConfig->staticTypeName();
        (void)adjustConfig->staticTypeName();
    }

    // 手动注册配置到DynamicConfigManager
    Config::ConfigService &       configService  = Config::ConfigService::getInstance();
    Config::DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 注册配置类型（使用实际的配置对象）
    dynamicManager.registerConfig(std::make_unique<Config::SystemConfigData>());
    dynamicManager.registerConfig(std::make_unique<PhotonSensor::FaculaConfigData>());
    dynamicManager.registerConfig(std::make_unique<LensAdjust::AdjustProcessConfigData>());

    // 初始化配置系统
    Config::ConfigInitializer configInitializer;
    Config::ConfigResult      result = configInitializer.initializeConfigs();
    if (!result.success) {
        QMessageBox::critical(nullptr, "Configuration Error", QString("Failed to initialize configuration files: %1").arg(result.message));
        LOG_ERROR(MyLogger::LogType::INIT, "Configuration initialization failed: " + result.message);
        return -1;
    }

    LOG_INFO(MyLogger::LogType::INIT, "Configuration system initialized successfully");

    // 创建主窗口
    cLensAdjust w;

    LOG_INFO(MyLogger::LogType::INIT, "lenAdjust started successfully");

    w.show();
    return a.exec();
}

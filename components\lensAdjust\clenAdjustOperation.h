#ifndef _CLEN_ADJUST_OPERATION_H_
#define _CLEN_ADJUST_OPERATION_H_

#include <QObject>
#include <QTableWidgetItem>
#include <QThread>
#include <QVector>
#include <stdint.h>


#include "IComm.h"
#include "ILoad.h"
#include "lensReadIni.h"
#include "processListB.h"
#include "sensorBoardFactory.h"


#include "IclensMachine.h"

#include "lensAdjustSerial.h"

#include "faculaContext.h"
#include "saveExcel.h"

#include "lenSqlHandle.h"
#include "myConnSql.h"

//***************************************其他配置***************************


namespace NClen {
typedef struct {
    QString cur_port_name;  //实时显示端口
    QString port_name;      //使用端口
    QString cur_dev_port_name;
    QString dev_port_name;

    bool                          is_button_close;
    IClensMachine::EMode          mode;
    IFaculaFactory::ERxFaculaType facula_type;  //光斑类型
    IPhotonSensor::EFaculaForm    facula_form;  //光斑形态
    QString                       target_area;
    uint16_t                      peak_throld;  //

} StUiConfig;
}  // namespace NClen


//*************************************光斑数据***************************
// class CFaculaContext;

class CLenAdjustOpt : public QObject {
    Q_OBJECT

  public:
    CLenAdjustOpt(const NClen::StUiConfig &config);
    ~CLenAdjustOpt();

    //*****************************************运行步骤*******************
    // enum EProcessStep {
    //     eOPEN_SERIAL = 0,
    //     eLEN_CAP     = 1,
    //     eCALIB_MODE,
    //     eCHIP_ID,
    //     eVERSION,
    //     eGREY_MODE,
    //     eLEN_STATUS,  //镜片抓取状态
    //     eORIGIN_LOC,  //
    //     eMAP_DATA,
    //     eADJUST_LEN,
    //     eADJUSTED_LOC,
    //     eTEST,
    //     eSOLID,
    //     eSOLID_STATUS,
    //     eRETEST,
    //     eDEFLATE,
    //     eRETEST2,
    //     eCOMPLETE,
    // };
    // for T5
    enum EProcessStep {
        eOPEN_SERIAL = 0,
        eLEN_CAP,
        eLEN_STATUS,  //镜片抓取状态/机台到位状态

        eCALIB_MODE,
        eCHIP_ID,
        eVERSION,
        eGREY_MODE,

        eVISION_ADJUST,
        eGET_VISION_STATUS,

        eORIGIN_LOC,  //

        eMAP_DATA,
        eADJUST_LEN,
        eGET_MOVE_STATUS,

        eADJUSTED_LOC,
        eTEST,
        eSOLID,
        eSOLID_STATUS,
        eRETEST,
        eDEFLATE,
        eRETEST2,
        eCOMPLETE,
    };
    Q_ENUM(EProcessStep);

  private:
    /*2. 通信状态*/
    typedef struct {
        uint check_error_cnt;
        uint timeout_cnt;
    } ST_COMM_STATUS;

    // 配置加载方法
    void loadConfigurationParameters();
    void setDefaultConfiguration();

    //******************************** result ******************8
    //* 单步骤光斑结果
    typedef struct {
        QByteArray                 mp_origin_data;  //原光斑数据
        QVector<QVector<uint32_t>> map_matrix;      // MP matrix 数据
        //        QVector<uint32_t>                    facula_mp; //光斑对应通道
        uint8_t result;
    } StSubResult;

    //* 全过程结果数据
    typedef struct {
        QString           chip_id;
        QString           sensor_version;
        QVector<float>    origin_loc;
        QVector<float>    final_loc;
        StSubResult       adjustResult;
        StSubResult       solidResult;
        StSubResult       finalResult;
        QVector<uint32_t> target_map;
        bool              final_result;
        QString           fatal_reason;
    } StResult;

    typedef union {
        uint16_t errors;
        struct {
            uint16_t serial_open : 1;
            uint16_t version : 1;
            uint16_t chip_id : 1;
            uint16_t xy_limit : 1;
            uint16_t z_limit : 1;
            uint16_t device_move : 1;
        } all_error;
    } UProcessItemDds;

    //* process items
    QMap<int, QString> mm_process_items_discribe{
        {0x0000, " ok"},
        {0x0001, "串口打开失败 "},
        {0x0002, "版本异常 "},
        {0x0004, "芯片ID异常 "},
        {0x0008, "XY轴限位 "},
        {0x0010, "Z轴限位 "},
        {0x0020, "机台控制指令发送异常 "},
        {0x0040, ""},
        {0x0080, ""},
    };


    //    enum class EDdsMes {
    //        eSERIAL_OPEN                = 1<<0,
    //        eVERSION                    = 1<<1,
    //        eCHIP_ID                    = 1<<2,
    //        eMOTOR_MOVE_ERROR           = 1<<3,
    //        eMOTOR_ANGLE_ERROR          = 1<<4,
    //        eDIST_PROCESS_ERROR         = 1<<5,
    //    };

    enum class EError_type {
        ePROCESS_STEP_ERROR = 0,  //流程收发异常
        ePROCESS_ITEM_ERROR = 1,
        eADJUST_STEP_ERROR  = 2,
        eFACULA_JUDGE_ERRPR = 3,
        eMES_ERROR          = 4,
    };

    typedef struct {
        UProcessItemDds process_item_dds;  //测试项存在异常
                                           //        uint32_t                    process_item_dds;
        uint32_t process_step_dds;         //步骤执行异常，异常显示内容比较固定，不用单独显示
        //        uint32_t                    adjust_step_dds; //调节步骤异常，窗口显示->手动干预，不记录在 dds
        //        uint32_t                    facula_judge_dds; //光斑判定异常，存分部判定结果中，不记录在 dds
        uint32_t mes_dds;  // mes写入异常, 窗口显示
    } StDds;

    //* 全测试计数
    typedef struct {
        uint  product_total_num;  //产品测试总数
        uint  good_product_num;   //良品数
        uint  bad_product_num;    //不良数
        float bad_product_rate;   //不良率
        float process_time;       //当前测试时间
        float aver_process_time;  //平均测试时间
    } StProductDetectInfo;

    //*******************************调节***********************
    //* 光斑要求：最大强度，中心侧边通道差值，连续通过次数
    //* 调节参数(由文本配置，故直接注释）

    //  typedef struct{
    //      bool                direction_flag; //
    //  } StDeviceCtrl;

    //* 配置输入
    ILoad *                  mi_load_ = nullptr;
    QMap<QString, int>       m_xml_param;
    const NClen::StUiConfig *mst_config_ = nullptr;

    //    CTableViewModule* mc_auto_grey_map_ = nullptr;
    //    CTableViewModule* mc_manual_grey_map_ = nullptr;
    //    CTableViewModule* mc_expand_grey_map_ = nullptr;

    //* 运行/状态
    int      m_timerId;
    uint16_t m_port_update_cnt;

    typedef CProcessListB<CLenAdjustOpt, EExecStatus, EProcessStep> TClen_process;
    TClen_process *                                                 mc_processList_  = nullptr;
    TClen_process::StStepRecord *                                   mst_task_status_ = nullptr;

  public:
    QVector<TClen_process::StTask>          mv_task_list;
    ISensorBoardFactory::StFaculaSensorInfo mst_facula_sensor_info;  //光斑、光感应芯片信息

    ITable::StTableInfo getOriginTableInfo();
    ITable::StTableInfo getExpandTableInfo();

  private:
    //* 子线程
    CLensAdjustSerial *m_serial_thread_ = nullptr;
    QThread *          m_sub_thread_    = nullptr;

    static const uint16_t s_timer_ms = 10;

    //* 端口接口
    IComm *mi_icomm_     = nullptr;  //主端口
    IComm *mi_icomm_dev_ = nullptr;  //设备端口

    //* devices
    ITopBoard *    mi_top_board_    = nullptr;  //设备
    IClensMachine *mi_clen_machine_ = nullptr;

    //* communication status
    StCommunicateStatus *mst_comm_status_ = nullptr;

    //* 数据
    QByteArray m_origin_bytes;
    //    typedef struct {
    //        uint8_t merge_data_cnt;
    //        QVector<uint32_t> map_data_cache;
    //    } StMergeData;
    //    StMergeData *mst_merge_data_;
    //    uint8_t m_cell_num_first;
    //    QVector<uint32_t> m_map_data_cache;

    QVector<float> m_loc_cache;

    //    IFaculaAdjust::StMapInfo *mst_map_info_ = nullptr;
    //    IFaculaAdjust::StMapTargetInfo *mst_target_map_info_ = nullptr;
    //    IFaculaAdjust::StMapData *m_map_data_ = nullptr;
    //    IFaculaAdjust::StMapInfo *mst_interpolation_map_info_ = nullptr;
    //    IFaculaAdjust::StMapData *m_map_interpolation_data_ = nullptr;

    //* 调节
    //  StFaculaAdjustConfig *mst_facula_adjust_conf_;
    //  StFaculaAdjust *mst_facula_adjust_ = nullptr;
    CFaculaContext *mc_facula_context_ = nullptr;

    uint16_t m_solid_times;      //固化计数 / 5ms
    uint16_t m_move_wait_times;  //移动等待时间

    //* debug
    QWidget *m_message_box_ = nullptr;

    //* 数据存储
    ISaveFile *                   mi_save_file_ = nullptr;
    QMap<QString, QVariant>       mm_result_data;  // = nullptr;
    CLenSqlHandle *               mc_sql_handle_      = nullptr;
    CLenSqlHandle::StWorkOrder *  mst_work_order_     = nullptr;
    CLenSqlHandle::StXpiddet *    mst_xpid_det_       = nullptr;
    CLenSqlHandle::StResultToMes *mst_mes_xsub2_data_ = nullptr;

    //* result
    StResult *mst_result_ = nullptr;
    StDds *   mst_dds_    = nullptr;

    //********************************
    virtual void timerEvent(QTimerEvent *event) override;

    void varibleInit(void);

    void resultInit(StSubResult *result_);

    void productTestInfoInit(StProductDetectInfo *product_info_);

    inline void sleepMs(uint16_t msec);
    void        sleepMsNonBlocking(uint16_t cnt, std::function<void()> callback);

    //******************************数据与调节处理************************************
    //    void mergeDataClean();
    //    QVector<uint32_t> dataMerge(const QVector<uint32_t> &origin_data, const uint8_t &times, const uint8_t &types);
    //    bool mapDataMerge(QByteArray &mp_origin_byte, StMergeData* merge_data_, QVector<uint32_t>* map_data_);
    //    bool originDataHanlde(QByteArray &mp_origin_bytes, IFaculaAdjust::StMapData* map_data_);
    //    void originDataManualHanlde();

    //    bool cellNumUpdate(const uint16_t &cell_num);
    //    bool targetFaculaArea(const QString &facula_loc, IFaculaAdjust::StMapInfo *map_info_, IFaculaAdjust::StMapTargetInfo *target_info_);
    //    bool targetFaculaArea(IFaculaAdjust::StMapInfo *map_info_);

    void mapDataShow();

    //*******************************循环任务集***************************
    bool    portlistUpdate();
    QString sensorVersionParse(const QByteArray &version);
    bool    checkVersion(const QString &version);

    //********************************** MES相关 ************************
    bool mesDataHandle(CLenSqlHandle::StResultToMes *st_mes_data_);

    //************************************ 异常处理 ************************
    QString errorInfoPack(EError_type error_type, const uint32_t &error_code);
    //    QString errorHandleCallbackFunction(EError_type error_type, const uint16_t &error_code, uint8_t show_type);

    EExecStatus readyStep(void);
    EExecStatus readyAck(void);

    EExecStatus startStep(void);  //开始抓取镜片
    EExecStatus startAck(void);

    EExecStatus calibMode(void);
    EExecStatus calibModeAck(void);

    EExecStatus chipIdStep(void);
    EExecStatus chipIdAck(void);

    EExecStatus sensorVersionStep(void);
    EExecStatus sensorVersionAck(void);

    EExecStatus lensCapped(void);  //镜片抓取完毕
    EExecStatus lensCappedAck(void);

    EExecStatus getOriginLoc(void);
    EExecStatus getOriginLocAck(void);

    EExecStatus lenMachineAutoAdjust(void);  //机台粗调
    EExecStatus lenMachineAutoAdjustAck(void);

    EExecStatus lenMachineAdjustStatus(void);
    EExecStatus lenMachineAdjustStatusAck(void);

    EExecStatus greyMapMode(void);
    EExecStatus greyMapModeAck(void);

    EExecStatus dataStep(void);
    EExecStatus dataAck(void);

    EExecStatus adjustStep(void);
    EExecStatus adjustAck(void);

    EExecStatus getMoveStatusStep(void);
    EExecStatus getMoveStatusAck(void);

    EExecStatus testStep(void);
    EExecStatus testAck(void);

    EExecStatus getAdjustedLoc(void);
    EExecStatus getAdjustedLocAck(void);

    EExecStatus solidStep(void);
    EExecStatus solidAck(void);

    EExecStatus solidStatusStep(void);
    EExecStatus solidStatusAck(void);

    EExecStatus retestStep(void);  //复测
    EExecStatus retestAck(void);

    EExecStatus deflateStep(void);  //放气
    EExecStatus deflateACK(void);

    EExecStatus retest2Step(void);
    EExecStatus retest2Ack(void);

    EExecStatus compStep(void);
    EExecStatus compAck(void);

  signals:
    //* status update signal
    //  void stepStatusSignal(EProcessStep step, EExecStatus status);
    void initErrorSignal(const QString &info);
    void stepStatusSignal(const int16_t &step, const int16_t &status);
    //    void stepErrorSignal(const int16_t &step, const QString &error);
    void moduleInfoShowSignal(const bool &is_error, const QString &info);


    void subThreadSignal(const bool &is_exc);

    //* port update signal
    void portUpdateSignal(QStringList *port_list_, bool port_flag);
    void devicePortUpdateSignal(QStringList *port_list_, bool port_flag);

    //* step ack signal
    void readySignal(bool is_open);

    void startAckSignal(void);

    void originLocSignal(const float &loc_x, const float &loc_y, const float &loc_z);

    void unnormalViewSignal(const bool &is_unnormal);

    void dataAckSignal(const uint &max, const QVector<QVector<uint32_t>> &matrix, const uint &max2, const QVector<QVector<uint32_t>> &matrix2);

    void adjustAckSignal(const int16_t &loc_x, const int16_t &loc_y, const int16_t &loc_z);

    void adjustedLocSignal(const float &loc_x, const float &loc_y, const float &loc_z);

    //    void mainMapSizeSignal(const uint8_t &xlen, const uint8_t &ylen);
    //  void subMapSizeSignal(const uint8_t &xlen, const uint8_t &ylen);

    //* result show signals
    void resultSignal(EResult result, const uint8_t &result_index);

    void compAckSignal(bool is_comp);

    void productTestInfoSignal(const bool &is_clean);

    //    void errorAckShow(const QString &error_tring);

  private slots:
    void dataReceive(IClensMachine::ECommAck step, ECommStatus status, QByteArray bytes);
    void dataReceive(IClensMachine::ECommAck step, ECommStatus status, QVector<float> buff);
    void sensorDataReceive(ITopBoard::ECommStep step, ECommStatus status, QByteArray bytes);

  public:
    QMap<QString, QString> m_product_info;
    StProductDetectInfo *  mst_product_detect_info_ = nullptr;

    C3dHandMachine::St3D<int16_t> *mst_move_distance_ = nullptr;
    void                           clenMoveDelta(void);
};

#endif

/**************************************
 * 维系 所有设备类对象
 * 维系 父类名称/子类名称/子类对象
 * **************************************/

#ifndef FACULA_FACTORY_H
#define FACULA_FACTORY_H

#include "faculaCircle.h"
#include "faculaDouble.h"
#include "faculaHorizontal.h"
#include "faculaVertical.h"


class IFaculaFactory
{
public:
    enum ERxFaculaType {
        circle, //圆形光斑
        horizontal, //横光斑
        vertical, //竖光斑
        double_disc, //双瓣
        poisson, //？
        irregular, //不规则
    };

    // 单例模式
    static IFaculaFactory& getInstance() {
        static IFaculaFactory instance;
        return instance;
    }

    IFaculaAdjust* faculaAdjustCreate(const ERxFaculaType &rx_facula_type, const IFaculaAdjust::StMapInfo &map_info);

private:
    explicit IFaculaFactory();
    ~IFaculaFactory();
};

#endif // IFaculaFactory_H

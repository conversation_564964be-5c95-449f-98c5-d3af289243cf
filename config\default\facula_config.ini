; Facula 配置文件
; 版本: 1.0.0
; 描述: 光斑检测和调节配置，包含所有XML参数

[motion_control]
; 初始移动距离配置
initial_x_dist=0              ; 初始X移动距离/um (int)
initial_y_dist=0              ; 初始Y移动距离/um (int)
initial_z_dist=0              ; 初始Z移动距离/um (int)
default_z_direct=1            ; 默认Z轴方向 (int8_t)
z_move_step=3                 ; Z轴移动步进(脉冲数) (int)

[search_parameters]
; 光斑搜索配置
find_origin_raduis=140        ; 初始寻找光斑半径/um (int)
find_angle_step=20            ; 寻找光斑角度步进/° (int)
find_radius_step=140          ; 寻找光斑半径步进/um (int)
find_times=4                  ; 寻找光斑次数 (int)

[threshold_parameters]
; 阈值配置
peak_ok_threshold=550         ; 达到后不再查找继续查找 (int)
peak_threshold=200            ; 基础peak阈值 (int)
peak_max_threshold=4000       ; 最大peak阈值 (int)
edge_peak_threshold=180       ; 外圈光斑光强最小阈值 (int)

[symmetry_parameters]
; 对称性调节参数
Amp_select=30                 ; 十字光斑特定通道调节 (int)
LR_peak_offset=0              ; 左右通道偏移 (int)
UD_peak_offset=0              ; 上下通道偏移 (int)
ALR_mp_peak=20                ; 左右通道peak比例*100 (int)
ALR_mp_peak_threshold=100     ; 左右通道容差 (int)
AUD_mp_peak=0                 ; 上下通道peak比例*100 (int)
AUD_mp_peak_threshold=100     ; 上下通道容差 (int)
ACR_peak_delta=120            ; 中心与十字光斑光强差值最小阈值 (int)
CR_peak_delta=50              ; 中心与周围差值阈值 (int)
ARR_peak_delta=40             ; 调节peak差值 (int)
Aedge_peak_threshold=180      ; 边缘peak阈值 (int)
AMax_peak=3000                ; 最大peak限制 (int)

[advanced_parameters]
; 高级调节参数
FT_adjust_peak_radio_threshold=80     ; 调节模式peak比例阈值 (int)
FT_solid_peak_radio_threshold=85      ; 固化模式peak比例阈值 (int)
FT_deflate_peak_radio_threshold=75    ; 收缩模式peak比例阈值 (int)
Uaround_peak_threshold=100            ; 周围peak阈值 (int)
Apeak_offset_radio=50                 ; peak偏移比例 (int)
#pragma once

#include "../../../components/configModule/ConfigTypeRegistry.h"
#include "../../../components/configModule/IConfigData.h"
#include <QString>
#include <QVariantMap>

namespace LensAdjust {

/**
 * @brief 产品检测信息配置数据
 *
 * 管理产品检测过程中的统计信息：
 * - 产品总数、良品数、不良品数
 * - 不良率统计
 * - 检测时间统计
 *
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在lensAdjust模块中定义和管理
 * - 专门用于产品检测统计
 * - 支持实时更新和持久化存储
 */
class ProductDetectConfigData : public Config::BaseConfigData<ProductDetectConfigData> {
  public:
    ProductDetectConfigData();
    ~ProductDetectConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() {
        return "ProductDetect";
    }

    // IConfigData接口实现
    QString getTypeName() const override {
        return staticTypeName();
    }
    QString getVersion() const override {
        return "1.0.0";
    }
    QString getDescription() const override {
        return "产品检测统计信息配置，包含产品数量、质量统计和时间统计";
    }

    QVariantMap toVariantMap() const override;
    bool        fromVariantMap(const QVariantMap &data) override;
    bool        validate() const override;
    void        setDefaults() override;

    QStringList getFieldNames() const override;
    QString     getFieldType(const QString &fieldName) const override;
    QString     getFieldDescription(const QString &fieldName) const override;
    bool        hasField(const QString &fieldName) const override;
    QVariant    getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool        setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool        resetField(const QString &fieldName) override;

    // 产品数量统计访问接口
    /**
     * @brief 获取产品总数
     * @return 产品总数
     */
    uint32_t getProductTotalNum() const {
        return product_total_num;
    }

    /**
     * @brief 设置产品总数
     * @param num 产品总数
     */
    void setProductTotalNum(uint32_t num) {
        product_total_num = num;
    }

    /**
     * @brief 获取良品数
     * @return 良品数
     */
    uint32_t getGoodProductNum() const {
        return good_product_num;
    }

    /**
     * @brief 设置良品数
     * @param num 良品数
     */
    void setGoodProductNum(uint32_t num) {
        good_product_num = num;
    }

    /**
     * @brief 获取不良品数
     * @return 不良品数
     */
    uint32_t getBadProductNum() const {
        return bad_product_num;
    }

    /**
     * @brief 设置不良品数
     * @param num 不良品数
     */
    void setBadProductNum(uint32_t num) {
        bad_product_num = num;
    }

    /**
     * @brief 获取不良率
     * @return 不良率 (0.0-1.0)
     */
    float getBadProductRate() const {
        return bad_product_rate;
    }

    /**
     * @brief 设置不良率
     * @param rate 不良率 (0.0-1.0)
     */
    void setBadProductRate(float rate) {
        bad_product_rate = rate;
    }

    /**
     * @brief 获取单次检测时长
     * @return 单次检测时长 (秒)
     */
    float getProcessTime() const {
        return process_time;
    }

    /**
     * @brief 设置单次检测时长
     * @param time 单次检测时长 (秒)
     */
    void setProcessTime(float time) {
        process_time = time;
    }

    /**
     * @brief 获取平均检测时长
     * @return 平均检测时长 (秒)
     */
    float getAverProcessTime() const {
        return aver_process_time;
    }

    /**
     * @brief 设置平均检测时长
     * @param time 平均检测时长 (秒)
     */
    void setAverProcessTime(float time) {
        aver_process_time = time;
    }

    // 便利方法
    /**
     * @brief 增加产品总数
     * @param count 增加数量，默认为1
     */
    void incrementProductTotal(uint32_t count = 1) {
        product_total_num += count;
        updateBadProductRate();
    }

    /**
     * @brief 增加良品数
     * @param count 增加数量，默认为1
     */
    void incrementGoodProduct(uint32_t count = 1) {
        good_product_num += count;
        updateBadProductRate();
    }

    /**
     * @brief 增加不良品数
     * @param count 增加数量，默认为1
     */
    void incrementBadProduct(uint32_t count = 1) {
        bad_product_num += count;
        updateBadProductRate();
    }

    /**
     * @brief 重置所有统计数据
     */
    void resetStatistics() {
        product_total_num = 0;
        good_product_num = 0;
        bad_product_num = 0;
        bad_product_rate = 0.0f;
        process_time = 0.0f;
        aver_process_time = 0.0f;
    }

    /**
     * @brief 获取统计摘要信息
     * @return 统计摘要字符串
     */
    QString getStatisticsSummary() const;

  private:
    /**
     * @brief 更新不良率
     */
    void updateBadProductRate() {
        if (product_total_num > 0) {
            bad_product_rate = static_cast<float>(bad_product_num) / static_cast<float>(product_total_num);
        } else {
            bad_product_rate = 0.0f;
        }
    }

    // 配置数据成员
    uint32_t product_total_num;  // 总测试模组数
    uint32_t good_product_num;   // 合格品数
    uint32_t bad_product_num;    // 不良品数
    float    bad_product_rate;   // 不良率
    float    process_time;       // 单次检测时长(秒)
    float    aver_process_time;  // 平均检测时长(秒)

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;
};

}  // namespace LensAdjust

#pragma once

#include "ConfigTypeRegistry.h"
#include "IConfigData.h"
#include <QString>
#include <QVariantMap>

namespace Config {

/**
 * @brief 系统配置数据
 *
 * 管理系统级别的所有配置参数：
 * - 传感器板卡版本信息
 * - MES系统配置
 * - 设备配置信息
 *
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在configModule中定义和管理
 * - 专门用于系统级配置
 * - 支持参数验证和默认值设置
 */
class SystemConfigData : public BaseConfigData<SystemConfigData> {
  public:
    SystemConfigData();
    ~SystemConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() {
        return "System";
    }

    // IConfigData接口实现
    QString getTypeName() const override {
        return staticTypeName();
    }
    QString getVersion() const override {
        return "1.0.0";
    }
    QString getDescription() const override {
        return "系统级配置，包含版本信息、MES系统配置和设备信息";
    }

    QVariantMap toVariantMap() const override;
    bool        fromVariantMap(const QVariantMap &data) override;
    bool        validate() const override;
    void        setDefaults() override;

    // 配置结构体定义
    struct SensorBoardConfig {
        QString version = "*******";  // 传感器板卡版本号
    };

    struct MesConfig {
        QString userid      = "admin";  // 用户ID，超级管理员：admin；其他示例：001
        QString op          = "105";    // 操作编号，默认105
        QString work_number = "10001";  // 工单号
        QString work_domain = "001";    // 工作域标识符
    };

    struct DeviceConfig {
        uint8_t  sensor_device       = 4;       // 设备类型：1-D4/2-T4/3-D6/4-T5
        uint32_t sensor_device_baud  = 230400;  // 设备波特率，通常为230400
        uint8_t  station_number      = 1;       // 工站号，默认为1
        uint8_t  clens_machine_brand = 3;       // 镜片调节设备品牌：1-顺拓；2-清河；3-清河视觉
    };

    // 配置访问方法（只读）
    const SensorBoardConfig &getSensorBoardConfig() const {
        return sensor_board_config_;
    }
    const MesConfig &getMesConfig() const {
        return mes_config_;
    }
    const DeviceConfig &getDeviceConfig() const {
        return device_config_;
    }

    // 配置修改方法（保持向后兼容）
    void setSensorBoardVersion(const QString &version) {
        sensor_board_config_.version = version;
    }
    void setUserId(const QString &id) {
        mes_config_.userid = id;
    }
    void setOp(const QString &operation) {
        mes_config_.op = operation;
    }
    void setWorkNumber(const QString &number) {
        mes_config_.work_number = number;
    }
    void setWorkDomain(const QString &domain) {
        mes_config_.work_domain = domain;
    }
    void setSensorDevice(uint8_t device) {
        device_config_.sensor_device = device;
    }
    void setSensorDeviceBaud(uint32_t baud) {
        device_config_.sensor_device_baud = baud;
    }
    void setStationNumber(uint8_t number) {
        device_config_.station_number = number;
    }
    void setClensMachineBrand(uint8_t brand) {
        device_config_.clens_machine_brand = brand;
    }

    // 保持向后兼容的getter方法
    QString getSensorBoardVersion() const {
        return sensor_board_config_.version;
    }
    QString getUserId() const {
        return mes_config_.userid;
    }
    QString getOp() const {
        return mes_config_.op;
    }
    QString getWorkNumber() const {
        return mes_config_.work_number;
    }
    QString getWorkDomain() const {
        return mes_config_.work_domain;
    }
    uint8_t getSensorDevice() const {
        return device_config_.sensor_device;
    }
    uint32_t getSensorDeviceBaud() const {
        return device_config_.sensor_device_baud;
    }
    uint8_t getStationNumber() const {
        return device_config_.station_number;
    }
    uint8_t getClensMachineBrand() const {
        return device_config_.clens_machine_brand;
    }

    // 配置字段名称列表
    QStringList getFieldNames() const override;
    QString     getFieldType(const QString &fieldName) const override;
    QString     getFieldDescription(const QString &fieldName) const override;

    // IConfigData接口实现
    bool     hasField(const QString &fieldName) const override;
    QVariant getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool     setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool     resetField(const QString &fieldName) override;

    // 配置验证
    bool isValidSensorDevice(uint8_t device) const;
    bool isValidMachineBrand(uint8_t brand) const;
    bool isValidBaudRate(uint32_t baud) const;

    // 配置摘要
    QString getConfigSummary() const;
    bool    isDefaultConfig() const;

  private:
    // 配置数据存储
    SensorBoardConfig sensor_board_config_;
    MesConfig         mes_config_;
    DeviceConfig      device_config_;

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;

    // 有效值定义
    static const QList<uint8_t>  s_validSensorDevices;
    static const QList<uint8_t>  s_validMachineBrands;
    static const QList<uint32_t> s_validBaudRates;
};

}  // namespace Config

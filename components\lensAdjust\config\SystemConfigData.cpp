#include "SystemConfigData.h"
#include <QDebug>

namespace Config {

// 静态成员初始化
const QMap<QString, QString> SystemConfigData::s_fieldTypes = {{"sensor_board_version", "QString"},
                                                               {"userid", "QString"},
                                                               {"op", "QString"},
                                                               {"work_number", "QString"},
                                                               {"work_domain", "QString"},
                                                               {"sensor_device", "uint8_t"},
                                                               {"sensor_device_baud", "uint32_t"},
                                                               {"station_number", "uint8_t"},
                                                               {"clens_machine_brand", "uint8_t"}};

const QMap<QString, QString> SystemConfigData::s_fieldDescriptions = {{"sensor_board_version", "传感器板卡版本号"},
                                                                      {"userid", "用户ID，超级管理员：admin；其他示例：001"},
                                                                      {"op", "操作编号，默认105"},
                                                                      {"work_number", "工单号"},
                                                                      {"work_domain", "工作域标识符"},
                                                                      {"sensor_device", "设备类型：1-D4/2-T4/3-D6/4-T5"},
                                                                      {"sensor_device_baud", "设备波特率，通常为230400"},
                                                                      {"station_number", "工站号，默认为1"},
                                                                      {"clens_machine_brand", "镜片调节设备品牌：1-顺拓；2-清河；3-清河视觉"}};

const QList<uint8_t>  SystemConfigData::s_validSensorDevices = {1, 2, 3, 4};
const QList<uint8_t>  SystemConfigData::s_validMachineBrands = {1, 2, 3};
const QList<uint32_t> SystemConfigData::s_validBaudRates     = {9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600};

SystemConfigData::SystemConfigData() {
    // 结构体会自动使用默认值初始化
    setDefaults();
}

QVariantMap SystemConfigData::toVariantMap() const {
    QVariantMap map;

    // 传感器板卡信息
    QVariantMap sensorBoard;
    sensorBoard["version"] = sensor_board_config_.version;
    map["sensor_board"]    = sensorBoard;

    // MES系统配置
    QVariantMap mes;
    mes["userid"]      = mes_config_.userid;
    mes["op"]          = mes_config_.op;
    mes["work_number"] = mes_config_.work_number;
    mes["work_domain"] = mes_config_.work_domain;
    map["mes"]         = mes;

    // 设备配置
    QVariantMap device;
    device["sensor_device"]       = device_config_.sensor_device;
    device["sensor_device_baud"]  = device_config_.sensor_device_baud;
    device["station_number"]      = device_config_.station_number;
    device["clens_machine_brand"] = device_config_.clens_machine_brand;
    map["device"]                 = device;

    // 添加元信息
    map["_type"]    = getTypeName();
    map["_version"] = getVersion();

    return map;
}

bool SystemConfigData::fromVariantMap(const QVariantMap &data) {
    try {
        // 读取传感器板卡信息
        if (data.contains("sensor_board")) {
            QVariantMap sensorBoard      = data["sensor_board"].toMap();
            sensor_board_config_.version = sensorBoard.value("version", "2.6.1.6").toString();
        }

        // 读取MES系统配置
        if (data.contains("mes")) {
            QVariantMap mes         = data["mes"].toMap();
            mes_config_.userid      = mes.value("userid", "admin").toString();
            mes_config_.op          = mes.value("op", "105").toString();
            mes_config_.work_number = mes.value("work_number", "10001").toString();
            mes_config_.work_domain = mes.value("work_domain", "001").toString();
        }

        // 读取设备配置
        if (data.contains("device")) {
            QVariantMap device                 = data["device"].toMap();
            device_config_.sensor_device       = device.value("sensor_device", 4).toUInt();
            device_config_.sensor_device_baud  = device.value("sensor_device_baud", 230400).toUInt();
            device_config_.station_number      = device.value("station_number", 1).toUInt();
            device_config_.clens_machine_brand = device.value("clens_machine_brand", 3).toUInt();
        }

        logInfo("System config loaded successfully");
        return true;

    } catch (const std::exception &e) {
        logError(QString("Failed to load system config: %1").arg(e.what()));
        return false;
    }
}

bool SystemConfigData::validate() const {
    // 验证传感器设备类型
    if (!isValidSensorDevice(device_config_.sensor_device)) {
        logError(QString("Invalid sensor device: %1").arg(device_config_.sensor_device));
        return false;
    }

    // 验证机器品牌
    if (!isValidMachineBrand(device_config_.clens_machine_brand)) {
        logError(QString("Invalid machine brand: %1").arg(device_config_.clens_machine_brand));
        return false;
    }

    // 验证波特率
    if (!isValidBaudRate(device_config_.sensor_device_baud)) {
        logError(QString("Invalid baud rate: %1").arg(device_config_.sensor_device_baud));
        return false;
    }

    // 验证字符串字段不为空
    if (mes_config_.userid.isEmpty() || mes_config_.op.isEmpty() || mes_config_.work_number.isEmpty()) {
        logError("Required string fields cannot be empty");
        return false;
    }

    return true;
}

void SystemConfigData::setDefaults() {
    // 重置为结构体默认值
    sensor_board_config_ = SensorBoardConfig{};
    mes_config_          = MesConfig{};
    device_config_       = DeviceConfig{};

    logInfo("Set system config to default values");
}

QStringList SystemConfigData::getFieldNames() const {
    return s_fieldTypes.keys();
}

QString SystemConfigData::getFieldType(const QString &fieldName) const {
    return s_fieldTypes.value(fieldName, "unknown");
}

QString SystemConfigData::getFieldDescription(const QString &fieldName) const {
    return s_fieldDescriptions.value(fieldName, "No description available");
}

bool SystemConfigData::hasField(const QString &fieldName) const {
    return s_fieldTypes.contains(fieldName);
}

QVariant SystemConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    if (fieldName == "sensor_board_version")
        return sensor_board_config_.version;
    if (fieldName == "userid")
        return mes_config_.userid;
    if (fieldName == "op")
        return mes_config_.op;
    if (fieldName == "work_number")
        return mes_config_.work_number;
    if (fieldName == "work_domain")
        return mes_config_.work_domain;
    if (fieldName == "sensor_device")
        return device_config_.sensor_device;
    if (fieldName == "sensor_device_baud")
        return device_config_.sensor_device_baud;
    if (fieldName == "station_number")
        return device_config_.station_number;
    if (fieldName == "clens_machine_brand")
        return device_config_.clens_machine_brand;

    return defaultValue;
}

bool SystemConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    if (fieldName == "sensor_board_version") {
        sensor_board_config_.version = value.toString();
        return true;
    }
    if (fieldName == "userid") {
        mes_config_.userid = value.toString();
        return true;
    }
    if (fieldName == "op") {
        mes_config_.op = value.toString();
        return true;
    }
    if (fieldName == "work_number") {
        mes_config_.work_number = value.toString();
        return true;
    }
    if (fieldName == "work_domain") {
        mes_config_.work_domain = value.toString();
        return true;
    }
    if (fieldName == "sensor_device") {
        device_config_.sensor_device = value.toUInt();
        return true;
    }
    if (fieldName == "sensor_device_baud") {
        device_config_.sensor_device_baud = value.toUInt();
        return true;
    }
    if (fieldName == "station_number") {
        device_config_.station_number = value.toUInt();
        return true;
    }
    if (fieldName == "clens_machine_brand") {
        device_config_.clens_machine_brand = value.toUInt();
        return true;
    }

    return false;
}

bool SystemConfigData::resetField(const QString &fieldName) {
    if (fieldName == "sensor_board_version") {
        sensor_board_config_.version = "2.6.1.6";
        return true;
    }
    if (fieldName == "userid") {
        mes_config_.userid = "admin";
        return true;
    }
    if (fieldName == "op") {
        mes_config_.op = "105";
        return true;
    }
    if (fieldName == "work_number") {
        mes_config_.work_number = "10001";
        return true;
    }
    if (fieldName == "work_domain") {
        mes_config_.work_domain = "001";
        return true;
    }
    if (fieldName == "sensor_device") {
        device_config_.sensor_device = 4;
        return true;
    }
    if (fieldName == "sensor_device_baud") {
        device_config_.sensor_device_baud = 230400;
        return true;
    }
    if (fieldName == "station_number") {
        device_config_.station_number = 1;
        return true;
    }
    if (fieldName == "clens_machine_brand") {
        device_config_.clens_machine_brand = 3;
        return true;
    }

    return false;
}

bool SystemConfigData::isValidSensorDevice(uint8_t device) const {
    return s_validSensorDevices.contains(device);
}

bool SystemConfigData::isValidMachineBrand(uint8_t brand) const {
    return s_validMachineBrands.contains(brand);
}

bool SystemConfigData::isValidBaudRate(uint32_t baud) const {
    return s_validBaudRates.contains(baud);
}

QString SystemConfigData::getConfigSummary() const {
    return QString("系统配置: 设备=%1, 波特率=%2, 用户=%3, 机器品牌=%4")
        .arg(device_config_.sensor_device)
        .arg(device_config_.sensor_device_baud)
        .arg(mes_config_.userid)
        .arg(device_config_.clens_machine_brand);
}

bool SystemConfigData::isDefaultConfig() const {
    SensorBoardConfig defaultSensorBoard{};
    MesConfig         defaultMes{};
    DeviceConfig      defaultDevice{};

    return sensor_board_config_.version == defaultSensorBoard.version && mes_config_.userid == defaultMes.userid && mes_config_.op == defaultMes.op &&
           mes_config_.work_number == defaultMes.work_number && mes_config_.work_domain == defaultMes.work_domain &&
           device_config_.sensor_device == defaultDevice.sensor_device && device_config_.sensor_device_baud == defaultDevice.sensor_device_baud &&
           device_config_.station_number == defaultDevice.station_number && device_config_.clens_machine_brand == defaultDevice.clens_machine_brand;
}

}  // namespace Config

// 自动注册系统配置类型
REGISTER_CONFIG_TYPE(Config::SystemConfigData, "System", "1.0.0", "系统级配置，包含版本信息、MES系统配置和设备信息")

#include "IFaculaAdjust.h"
#include "../../components/configModule/ConfigService.h"
#include "../../qtDebug/qLog.h"
#include "config/AlgorithmConfigProvider.h"
#include "config/FaculaConfigData.h"
#include <QApplication>
#include <QFile>


QMap<int, QString> IFaculaAdjust::mm_adjust_step_discribe = {
    {0x0000, " OK"},
    {0x0001, "未找到光斑 "},
    {0x0002, "不能移动到目标区域 "},
    {0x0004, "无法对称分布 "},
    {0x0008, "调节超时"},
    {0x0010, ""},
    {0x0020, ""},
    {0x0040, ""},
    {0x0080, ""},
};

//* process items
QMap<int, QString> IFaculaAdjust::mm_facula_judge_discribe = {
    {0x0000, " ok"},
    {0x0001, "光斑中心不在目标区域 "},
    {0x0002, "光斑十字不对称 "},
    {0x0004, "光斑外圈不对称 "},
    {0x0008, "中心光斑强度异常 "},
    {0x0010, "外圈光斑强度异常"},
    {0x0020, "光斑中心与十字区域强度差值异常"},
    {0x0040, ""},
    {0x0080, ""},
};

IFaculaAdjust::IFaculaAdjust() : mi_load_(new CLoadXml) {
    // 旧配置系统已废弃，注释掉相关代码
    /*
    // 使用专门的配置提供者
    m_algorithmConfigProvider = std::make_unique<Algorithm::AlgorithmConfigProvider>();

    // 从算法配置提供者获取参数
    const auto &algorithmParams = m_algorithmConfigProvider->getAllParameters();
    // 转换QVariantMap到QMap<QString, int>
    for (auto it = algorithmParams.begin(); it != algorithmParams.end(); ++it) {
        m_xml_param[it.key()] = it.value().toInt();
    }

    // 记录算法参数加载日志
    qDebug() << "[INFO] Loaded algorithm parameters - find_origin_raduis:" << m_xml_param["find_origin_raduis"] << ", find_times:" << m_xml_param["find_times"]
             << ", peak_ok_threshold:" << m_xml_param["peak_ok_threshold"];

    qDebug() << "[INFO] Algorithm config - initial_dist(x:" << m_xml_param["initial_x_dist"] << ", y:" << m_xml_param["initial_y_dist"]
             << ", z:" << m_xml_param["initial_z_dist"] << "), z_move_step:" << m_xml_param["z_move_step"];

    // 如果配置为空，使用默认值
    if (m_xml_param.isEmpty()) {
        qDebug() << "[WARNING] Algorithm config is empty, using default values";
        loadLegacyXmlConfig();
    }
    */

    // 新配置系统初始化
    qDebug() << "[INFO] IFaculaAdjust initialized with new configuration system";

    // 旧配置系统的对称性参数初始化（已废弃 - 注释掉）
    /*
    mm_symmetry_mp_param.insert(EFaculaJudgeMode::eFACULA_ADJUST,
                                StSymmetryInfo{
                                    C3dHandMachine::St3D<int16_t>{
                                        (int16_t)m_xml_param["ALR_mp_peak"],
                                        (int16_t)m_xml_param["AUD_mp_peak"],
                                        0,
                                    },
                                    C3dHandMachine::St3D<uint16_t>{
                                        (uint16_t)m_xml_param["ALR_mp_peak_threshold"],
                                        (uint16_t)m_xml_param["AUD_mp_peak_threshold"],
                                        0,
                                    },
                                });
    // ... 其他对称性参数初始化代码已注释
    */

    // 延迟初始化对称性参数，等配置系统完全准备好后再调用
    // initSymmetryParamsWithNewConfig(); // 将在首次使用时延迟初始化
}

IFaculaAdjust::~IFaculaAdjust() {
    if (mi_load_ != nullptr)
        delete mi_load_;
}

/**
 * @brief 根据单帧 数据长度 切换接收数据类型
 * @param senssor 型号，
 * @return
 */
// bool IFaculaAdjust::cellNumUpdate(const IPhotonSensor::ESensorModel &sensor_model, StMapInfo *st_map_info_, StMapInfo *st_sub_map_info_)
//{
//    bool result = true;
//    uint8_t xlen, ylen;

//    if(IPhotonSensor::mm_mpNum_link_dimension.find(sensor_model) != IPhotonSensor::mm_mpNum_link_dimension.end()) {
//        /*1. 表格尺寸更新*/
//        st_map_info_->xlens = IPhotonSensor::mm_mpNum_link_dimension[sensor_model].x;
//        st_map_info_->ylens = IPhotonSensor::mm_mpNum_link_dimension[sensor_model].y;

//        st_map_info_->mp_order = IPhotonSensor::mm_mpNum_link_dimension[sensor_model].order;
//        st_map_info_->sensor_direction = IPhotonSensor::mm_mpNum_link_dimension[sensor_model].direct;
//    }
//    else {
//        qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-e IFaculaAdjust/ sensor model:" << sensor_model;
//        return false;
//    }

//    /*1.1 原表格配置*/
////    m_map_data_->map_matrix.resize(ylen); //

//    /*1.2 拓展表格配置*/
//    xlen += st_map_info_->xlens%2 == 0?1:2;
//    ylen += st_map_info_->ylens%2 == 0?1:2;

//    if(st_sub_map_info_ != nullptr) {
////        m_map_interpolation_data_->map_matrix.resize(ylen); //
////        for (uint i = 0; i < ylen; i++) {
////            m_map_interpolation_data_->map_matrix[i].resize(xlen);
////        }
//        st_sub_map_info_->xlens = xlen;
//        st_sub_map_info_->ylens = ylen;
//    }

//    return result;
//}

/**
 * @brief 旧的XML配置加载方法（已废弃 - 注释掉）
 */
/*
void IFaculaAdjust::loadLegacyXmlConfig() {
    // 旧配置系统已完全废弃，不再使用
    qCritical() << "[IFaculaAdjust] DEPRECATED: loadLegacyXmlConfig() is no longer supported!";
    throw std::runtime_error("Legacy XML configuration system is deprecated!");
}
*/

// 新配置系统访问方法
const PhotonSensor::FaculaConfigData *IFaculaAdjust::getFaculaConfig() const {
    if (!m_faculaConfig) {
        try {
            Config::ConfigService &configService = Config::ConfigService::getInstance();
            m_faculaConfig                       = configService.getDynamicManager().getConfig<PhotonSensor::FaculaConfigData>("Facula");
            if (m_faculaConfig) {
                qDebug() << "[IFaculaAdjust] Facula config loaded successfully";
            } else {
                qWarning() << "[IFaculaAdjust] Failed to load Facula config";
            }
        } catch (const std::exception &e) {
            qCritical() << "[IFaculaAdjust] Exception loading config:" << e.what();
            m_faculaConfig = nullptr;
        }
    }
    return m_faculaConfig;
}

int IFaculaAdjust::getConfigParam(const QString &key, int defaultValue) const {
    // 尝试延迟初始化对称性参数
    const_cast<IFaculaAdjust *>(this)->initSymmetryParamsWithNewConfig();

    auto *config = getFaculaConfig();
    if (!config) {
        qWarning() << "[IFaculaAdjust] Config not available for key:" << key << ", using default:" << defaultValue;
        return defaultValue;  // 返回默认值而不是抛出异常
    }

    // 映射旧参数名到新方法（与CFaculaCircle相同的映射）
    if (key == "find_times")
        return config->getFindTimes();
    if (key == "discard_pack_num")
        return config->getDiscardPackNum();
    if (key == "find_radius_step")
        return config->getFindRadiusStep();
    if (key == "find_angle_step")
        return config->getFindAngleStep();
    if (key == "peak_ok_threshold")
        return config->getPeakOkThreshold();
    if (key == "default_z_direct")
        return config->getDefaultZDirect();
    if (key == "z_move_step")
        return config->getZMoveStep();
    if (key == "find_origin_raduis")
        return config->getFindOriginRadius();
    if (key == "Amp_select")
        return config->getAmpSelect();
    if (key == "ALR_mp_peak")
        return config->getALRMpPeak();
    if (key == "ALR_mp_peak_threshold")
        return config->getALRMpPeakThreshold();
    if (key == "AUD_mp_peak")
        return config->getAUDMpPeak();
    if (key == "AUD_mp_peak_threshold")
        return config->getAUDMpPeakThreshold();
    if (key == "ACR_peak_delta")
        return config->getACRPeakDelta();
    if (key == "CR_peak_delta")
        return config->getCRPeakDelta();
    if (key == "ARR_peak_delta")
        return config->getARRPeakDelta();
    if (key == "Aedge_peak_threshold")
        return config->getAedgePeakThreshold();
    if (key == "AMax_peak")
        return config->getAMaxPeak();
    if (key == "peak_threshold")
        return config->getPeakThreshold();
    if (key == "peak_max_threshold")
        return config->getPeakMaxThreshold();
    if (key == "edge_peak_threshold")
        return config->getEdgePeakThreshold();
    if (key == "LR_peak_offset")
        return config->getLRPeakOffset();
    if (key == "UD_peak_offset")
        return config->getUDPeakOffset();
    if (key == "FT_adjust_peak_radio_threshold")
        return config->getFTAdjustPeakRadioThreshold();
    if (key == "FT_solid_peak_radio_threshold")
        return config->getFTSolidPeakRadioThreshold();
    if (key == "FT_deflate_peak_radio_threshold")
        return config->getFTDeflatePeakRadioThreshold();
    if (key == "Uaround_peak_threshold")
        return config->getUaroundPeakThreshold();
    if (key == "Apeak_offset_radio")
        return config->getApeakOffsetRadio();


    // 不再支持旧配置系统，未知参数直接报错
    qCritical() << "[IFaculaAdjust] FATAL: Unknown config parameter:" << key;
    qCritical() << "[IFaculaAdjust] This parameter is not defined in the new configuration system!";
    throw std::runtime_error(QString("Unknown configuration parameter: %1").arg(key).toStdString());
}

// 使用新配置系统初始化对称性参数（延迟初始化）
void IFaculaAdjust::initSymmetryParamsWithNewConfig() {
    // 检查是否已经初始化过
    if (!mm_symmetry_mp_param.isEmpty()) {
        return;  // 已经初始化过，直接返回
    }

    auto *config = getFaculaConfig();
    if (!config) {
        qWarning() << "[IFaculaAdjust] Config not ready yet, will retry later";
        return;  // 配置未准备好，稍后重试
    }

    // 使用新配置系统初始化对称性参数
    mm_symmetry_mp_param.insert(EFaculaJudgeMode::eFACULA_ADJUST,
                                StSymmetryInfo{
                                    C3dHandMachine::St3D<int16_t>{
                                        (int16_t)config->getALRMpPeak(),
                                        (int16_t)config->getAUDMpPeak(),
                                        0,
                                    },
                                    C3dHandMachine::St3D<uint16_t>{
                                        (uint16_t)config->getALRMpPeakThreshold(),
                                        (uint16_t)config->getAUDMpPeakThreshold(),
                                        0,
                                    },
                                });
    mm_symmetry_mp_param.insert(EFaculaJudgeMode::eFACULA_ADJUST_TEST,
                                StSymmetryInfo{
                                    C3dHandMachine::St3D<int16_t>{
                                        (int16_t)config->getFTLRmpAdjustPeak(),
                                        (int16_t)config->getFTUDmpAdjustPeak(),
                                        0,
                                    },
                                    C3dHandMachine::St3D<uint16_t>{
                                        (uint16_t)config->getFTLRmpAdjustPeakThreshold(),
                                        (uint16_t)config->getFTUDmpAdjustPeakThreshold(),
                                        0,
                                    },
                                });
    mm_symmetry_mp_param.insert(EFaculaJudgeMode::eFACULA_SOLID_TEST,
                                StSymmetryInfo{
                                    C3dHandMachine::St3D<int16_t>{
                                        (int16_t)config->getFTLRmpSolidPeak(),
                                        (int16_t)config->getFTUDmpSolidPeak(),
                                        0,
                                    },
                                    C3dHandMachine::St3D<uint16_t>{
                                        (uint16_t)config->getFTLRmpSolidPeakThreshold(),
                                        (uint16_t)config->getFTUDmpSolidPeakThreshold(),
                                        0,
                                    },
                                });
    mm_symmetry_mp_param.insert(EFaculaJudgeMode::eFACUAL_DEFLATE_TEST,
                                StSymmetryInfo{
                                    C3dHandMachine::St3D<int16_t>{
                                        (int16_t)config->getFTLRmpDeflatePeak(),
                                        (int16_t)config->getFTUDmpDeflatePeak(),
                                        0,
                                    },
                                    C3dHandMachine::St3D<uint16_t>{
                                        (uint16_t)config->getFTLRmpDeflatePeakThreshold(),
                                        (uint16_t)config->getFTUDmpDeflatePeakThreshold(),
                                        0,
                                    },
                                });

    qDebug() << "[IFaculaAdjust] Symmetry parameters initialized with new configuration system";
}

# 光路调节本地配置重构方案

## 项目架构分析

当前项目采用模块化架构，光路调节功能是项目中的一个子功能模块（类似插件架构）。配置管理模块(`components/config`)是整个项目的基础设施，为所有功能模块提供统一的配置管理服务。

## 原则

- 单一来源
- 奥卡姆剃刀
- 关键信息原则
- 图表优先
- 

## 目录结构

```
bin\config\
├── lenAdjust\                        # 
│   ├── system_config.ini             # 系统配置 - lensAdjust 模块注册和加载
│   ├── adjustprocess_config.ini      # 调节流程配置 - lensAdjust 模块注册和加载
│   ├── facula_config.ini             # 光斑调节配置 - photonSensor 模块注册和加载
│   ├── algorithm_config.ini          # 算法配置 - photonSensor 模块注册和加载
│   ├── hardware_config.ini           # 硬件配置 - machine 模块注册和加载
```

## 配置参数对比分析

### 参数迁移映射表

| 原有配置                                | 原有参数                           | 重构后配置                              | 重构后参数                          | 迁移状态    | 备注              |
| ----------------------------------- | ------------------------------ | ---------------------------------- | ------------------------------ | ------- | --------------- |
| **系统级参数**                           |                                |                                    |                                |         |                 |
| clen_config.ini [SENSOR_BOARD]      | version                        | system_config.ini [sensor_board]   | version                        | ✅ 完全迁移  | 传感器版本号          |
| clen_config.ini [MES]               | userid                         | system_config.ini [mes]            | userid                         | ✅ 完全迁移  | 用户ID            |
| clen_config.ini [MES]               | op                             | system_config.ini [mes]            | op                             | ✅ 完全迁移  | 操作编号            |
| clen_config.ini [MES]               | work_number                    | system_config.ini [mes]            | work_number                    | ✅ 完全迁移  | 工单号             |
| clen_config.ini [MES]               | work_domain                    | system_config.ini [mes]            | work_domain                    | ✅ 完全迁移  | 工作域             |
| clen_config.ini [DEVICE]            | sensor_device                  | system_config.ini [device]         | sensor_device                  | ✅ 完全迁移  | 设备类型            |
| clen_config.ini [DEVICE]            | sensor_device_baud             | system_config.ini [device]         | sensor_device_baud             | ✅ 完全迁移  | 设备波特率           |
| clen_config.ini [DEVICE]            | station_number                 | system_config.ini [device]         | station_number                 | ✅ 完全迁移  | 工站号             |
| clen_config.ini [DEVICE]            | clens_machine_brand            | system_config.ini [device]         | clens_machine_brand            | ✅ 完全迁移  | 设备品牌            |
| **调节流程参数**                          |                                |                                    |                                |         |                 |
| clen_config.ini [ADJUST_PARAM]      | facula_center_loc_x            | facula_config.ini                  | facula_center_loc_x            | ✅ 完全迁移  | 光斑中心坐标x         |
| clen_config.ini [ADJUST_PARAM]      | facula_center_loc_y            | facula_config.ini                  | facula_center_loc_y            | ✅ 完全迁移  | 光斑中心坐标y         |
| clen_config.ini [ADJUST_PARAM]      | facula_ok_time                 | adjustprocess_config.ini [General] | facula_ok_times                | ✅ 完全迁移  | 判定次数            |
| clen_config.ini [ADJUST_PARAM]      | solid_time                     | adjustprocess_config.ini [General] | solid_time                     | ✅ 完全迁移  | 固化时间(毫秒)        |
| clen_config.ini [ADJUST_PARAM]      | facula_ng_handle               | adjustprocess_config.ini [General] | facula_ng_handle               | ✅ 完全迁移  | 异常处理方式          |
| **图像处理参数**                          |                                |                                    |                                |         |                 |
| clen_config.ini [FACULA_PROCESSING] | interpolation_type             | algorithm_config.ini               | interpolation_type             | ✅ 完全迁移  | 插值类型            |
| clen_config.ini [FACULA_PROCESSING] | filter_types                   | algorithm_config.ini               | filter_types                   | ✅ 完全迁移  | 滤波器类型           |
| clen_config.ini [FACULA_PROCESSING] | interpolation_offset           | algorithm_config.ini               | interpolation_offset           | ✅ 完全迁移  | 插值偏移量           |
| clen_config.ini [FACULA_PROCESSING] | filter_strength                | algorithm_config.ini               | filter_strength                | ✅ 完全迁移  | 全局滤波强度          |
| clen_config.ini [FACULA_PROCESSING] | kalman_strength                | algorithm_config.ini               | kalman_strength                | ✅ 完全迁移  | 卡尔曼滤波强度         |
| clen_config.ini [FACULA_PROCESSING] | gaussian_sigma                 | algorithm_config.ini               | gaussian_sigma                 | ✅ 完全迁移  | 高斯滤波标准差         |
| clen_config.ini [FACULA_PROCESSING] | gaussian_kernel_size           | algorithm_config.ini               | gaussian_kernel_size           | ✅ 完全迁移  | 高斯滤波核大小         |
| clen_config.ini [FACULA_PROCESSING] | bilateral_sigma_color          | algorithm_config.ini               | bilateral_sigma_color          | ✅ 完全迁移  | 双边滤波颜色标准差       |
| clen_config.ini [FACULA_PROCESSING] | bilateral_sigma_space          | algorithm_config.ini               | bilateral_sigma_space          | ✅ 完全迁移  | 双边滤波空间标准差       |
| clen_config.ini [FACULA_PROCESSING] | convolution_kernel_size        | algorithm_config.ini               | convolution_kernel_size        | ✅ 完全迁移  | 卷积核大小           |
| clen_config.ini [FACULA_PROCESSING] | convolution_preset             | algorithm_config.ini               | convolution_preset             | ✅ 完全迁移  | 卷积预设            |
| clen_config.ini [FACULA_PROCESSING] | median_kernel_size             | algorithm_config.ini               | median_kernel_size             | ✅ 完全迁移  | 中值滤波核大小         |
| clen_config.ini [FACULA_PROCESSING] | median_preset                  | algorithm_config.ini               | median_preset                  | ✅ 完全迁移  | 中值滤波预设          |
| clen_config.ini [FACULA_PROCESSING] | gaussian_preset                | algorithm_config.ini               | gaussian_preset                | ✅ 完全迁移  | 高斯滤波预设          |
| clen_config.ini [FACULA_PROCESSING] | bilateral_kernel_size          | algorithm_config.ini               | bilateral_kernel_size          | ✅ 完全迁移  | 双边滤波核大小         |
| clen_config.ini [FACULA_PROCESSING] | bilateral_preset               | algorithm_config.ini               | bilateral_preset               | ✅ 完全迁移  | 双边滤波预设          |
| clen_config.ini [FACULA_PROCESSING] | weighted_avg_kernel_size       | algorithm_config.ini               | weighted_avg_kernel_size       | ✅ 完全迁移  | 加权均值滤波核大小       |
| clen_config.ini [FACULA_PROCESSING] | weighted_avg_preset            | algorithm_config.ini               | weighted_avg_preset            | ✅ 完全迁移  | 加权均值滤波预设        |
| **光斑处理参数**                          |                                |                                    |                                |         |                 |
| clen_config.ini [FACULA_HANDLE]     | facula_handle_type             | facula_config.ini                  | facula_handle_type             | ✅ 完全迁移  | 光斑处理类型          |
| **光斑检测参数**                          |                                |                                    |                                |         |                 |
| -                                   | facula_center_channels         | facula_config.ini                  | facula_center_channels         | 🆕 新增参数 | 多通道光斑中心配置       |
| -                                   | facula_center_peak_threshold   | facula_config.ini                  | facula_center_peak_threshold   | 🆕 新增参数 | 多通道peak阈值       |
| **XML调节参数**                         |                                |                                    |                                |         |                 |
| clen_config.xml                     | initial_x_dist                 | facula_config.ini                  | initial_x_dist                 | ✅ 完全迁移  | 初始x移动距离         |
| clen_config.xml                     | initial_y_dist                 | facula_config.ini                  | initial_y_dist                 | ✅ 完全迁移  | 初始y移动距离         |
| clen_config.xml                     | initial_z_dist                 | facula_config.ini                  | initial_z_dist                 | ✅ 完全迁移  | 初始z移动距离         |
| clen_config.xml                     | find_origin_raduis             | facula_config.ini                  | find_origin_raduis             | ✅ 完全迁移  | 寻找光斑半径          |
| clen_config.xml                     | find_angle_step                | facula_config.ini                  | find_angle_step                | ✅ 完全迁移  | 寻找光斑角度步进        |
| clen_config.xml                     | find_radius_step               | facula_config.ini                  | find_radius_step               | ✅ 完全迁移  | 寻找光斑半径步进        |
| clen_config.xml                     | find_times                     | facula_config.ini                  | find_times                     | ✅ 完全迁移  | 寻找光斑次数          |
| clen_config.xml                     | discard_pack_num               | facula_config.ini                  | discard_pack_num               | ✅ 完全迁移  | 丢弃package数      |
| clen_config.xml                     | default_z_direct               | facula_config.ini                  | default_z_direct               | ✅ 完全迁移  | 默认Z轴移动方向        |
| clen_config.xml                     | z_move_step                    | facula_config.ini                  | z_move_step                    | ✅ 完全迁移  | z轴移动步进          |
| clen_config.xml                     | peak_ok_threshold              | facula_config.ini                  | peak_ok_threshold              | ✅ 完全迁移  | 达到后不再查找阈值       |
| clen_config.xml                     | Amp_select                     | facula_config.ini                  | Amp_select                     | ✅ 完全迁移  | 十字光斑特定通道调节      |
| clen_config.xml                     | ALR_mp_peak                    | facula_config.ini                  | ALR_mp_peak                    | ✅ 完全迁移  | 左右通道peak比例      |
| clen_config.xml                     | ALR_mp_peak_threshold          | facula_config.ini                  | ALR_mp_peak_threshold          | ✅ 完全迁移  | 左右通道容差          |
| clen_config.xml                     | AUD_mp_peak                    | facula_config.ini                  | AUD_mp_peak                    | ✅ 完全迁移  | 上下通道peak比例      |
| clen_config.xml                     | AUD_mp_peak_threshold          | facula_config.ini                  | AUD_mp_peak_threshold          | ✅ 完全迁移  | 上下通道容差          |
| clen_config.xml                     | Aedge_peak_threshold           | facula_config.ini                  | Aedge_peak_threshold           | ✅ 完全迁移  | 外圈光斑光强最小阈值      |
| clen_config.xml                     | ACR_peak_delta                 | facula_config.ini                  | ACR_peak_delta                 | ✅ 完全迁移  | 中心与十字光斑光强差值阈值   |
| clen_config.xml                     | ARR_peak_delta                 | facula_config.ini                  | ARR_peak_delta                 | ✅ 完全迁移  | 上下左右MP peak允许偏差 |
| clen_config.xml                     | AMax_peak                      | facula_config.ini                  | AMax_peak                      | ✅ 完全迁移  | 中心光斑允许最大值       |
| **XML判定参数**                         |                                |                                    |                                |         |                 |
| clen_config.xml                     | edge_peak_threshold            | facula_config.ini                  | edge_peak_threshold            | ✅ 完全迁移  | 四个角最低peak阈值     |
| clen_config.xml                     | peak_threshold                 | facula_config.ini                  | peak_threshold                 | ✅ 完全迁移  | 最低peak阈值        |
| clen_config.xml                     | peak_max_threshold             | facula_config.ini                  | peak_max_threshold             | ✅ 完全迁移  | 最高peak阈值        |
| clen_config.xml                     | CR_peak_delta                  | facula_config.ini                  | CR_peak_delta                  | ✅ 完全迁移  | 中间与周围MP光斑差值     |
| clen_config.xml                     | FT_LRmp_adjust_peak            | facula_config.ini                  | FT_LRmp_adjust_peak            | ✅ 完全迁移  | 调节判定左右通道peak    |
| clen_config.xml                     | FT_LRmp_adjust_peak_threshold  | facula_config.ini                  | FT_LRmp_adjust_peak_threshold  | ✅ 完全迁移  | 调节判定左右通道容差      |
| clen_config.xml                     | FT_UDmp_adjust_peak            | facula_config.ini                  | FT_UDmp_adjust_peak            | ✅ 完全迁移  | 调节判定上下通道peak    |
| clen_config.xml                     | FT_UDmp_adjust_peak_threshold  | facula_config.ini                  | FT_UDmp_adjust_peak_threshold  | ✅ 完全迁移  | 调节判定上下通道容差      |
| clen_config.xml                     | FT_LRmp_solid_peak             | facula_config.ini                  | FT_LRmp_solid_peak             | ✅ 完全迁移  | 固化判定左右通道peak    |
| clen_config.xml                     | FT_LRmp_solid_peak_threshold   | facula_config.ini                  | FT_LRmp_solid_peak_threshold   | ✅ 完全迁移  | 固化判定左右通道容差      |
| clen_config.xml                     | FT_UDmp_solid_peak             | facula_config.ini                  | FT_UDmp_solid_peak             | ✅ 完全迁移  | 固化判定上下通道peak    |
| clen_config.xml                     | FT_UDmp_solid_peak_threshold   | facula_config.ini                  | FT_UDmp_solid_peak_threshold   | ✅ 完全迁移  | 固化判定上下通道容差      |
| clen_config.xml                     | FT_LRmp_deflate_peak           | facula_config.ini                  | FT_LRmp_deflate_peak           | ✅ 完全迁移  | 放气判定左右通道peak    |
| clen_config.xml                     | FT_LRmp_deflate_peak_threshold | facula_config.ini                  | FT_LRmp_deflate_peak_threshold | ✅ 完全迁移  | 放气判定左右通道容差      |
| clen_config.xml                     | FT_UDmp_deflate_peak           | facula_config.ini                  | FT_UDmp_deflate_peak           | ✅ 完全迁移  | 放气判定上下通道peak    |
| clen_config.xml                     | FT_UDmp_deflate_peak_threshold | facula_config.ini                  | FT_UDmp_deflate_peak_threshold | ✅ 完全迁移  | 放气判定上下通道容差      |
| **硬件配置参数**                          |                                |                                    |                                |         |                 |
| clen_config_QH_SHJ.xml              | xy_radius_limit                | hardware_config.ini                | xy_radius_limit                | ✅ 完全迁移  | XY轴限位半径         |
| clen_config_QH_SHJ.xml              | z_radius_limit                 | hardware_config.ini                | z_radius_limit                 | ✅ 完全迁移  | Z轴限位            |
| clen_config_QH_SHJ.xml              | x_step_dist                    | hardware_config.ini                | x_step_dist                    | ✅ 完全迁移  | x单脉冲移动距离        |
| clen_config_QH_SHJ.xml              | y_step_dist                    | hardware_config.ini                | y_step_dist                    | ✅ 完全迁移  | y单脉冲移动距离        |
| clen_config_QH_SHJ.xml              | z_step_dist                    | hardware_config.ini                | z_step_dist                    | ✅ 完全迁移  | Z单脉冲移动距离        |

## 光路调节功能模块分析

### 光路调节功能涉及的模块

#### 🎯 关键模块职责分工

##### 1. **algorithm/imageProcessing/** - 图像处理算法模块
- **职责**: 提供底层图像处理算法实现
- **配置文件**: 无（算法库不直接加载配置）
- **主要功能**:
  - 插值算法：BilinearInterpolation, NoInterpolation等
  - 滤波算法：GaussianFilter, BilateralFilter, KalmanFilter等
  - 工厂模式：FilterFactory, InterpolationFactory
  - 接口定义：IImageFilter, IInterpolation
- **设计特点**:
  - 遵循SOLID原则的纯算法库
  - 通过工厂模式支持运行时算法选择
  - 不依赖配置系统，由上层模块调用

##### 2. **sensor/photonSensor/** - 光子传感器模块
- **职责**: 光斑检测和处理的业务逻辑
- **配置文件**: `algorithm_config.ini` (通过AlgorithmConfigData加载)
- **主要功能**:
  - 光斑检测：faculaCircle.cpp - 光斑中心定位
  - 光斑处理：faculaContext.cpp - 光斑数据处理
  - 配置管理：FaculaConfigData - 光斑相关配置
  - 算法适配：调用algorithm/imageProcessing/的算法
- **配置参数**:
  - 光斑中心坐标 (facula_center_loc_x/y)
  - 光斑检测阈值 (facula_threshold_min/max)
  - 图像处理参数 (interpolation_type, filter_types等)
  - 滤波器详细参数 (gaussian_sigma, kalman_strength等)

#### 3. 核心调节模块
- **clenAdjustOperation.cpp/h** - 主要调节逻辑
  - 加载参数：MES参数、调节参数、设备配置
  - 使用配置：facula_ok_time, solid_time, facula_ng_handle
  - 依赖模块：硬件控制、光斑检测

#### 4. 硬件控制模块
- **clensMachine系列** - 设备控制
  - 加载参数：设备类型、步进参数、限位参数
  - 使用配置：clens_machine_brand, z_move_step, 限位参数
  - 依赖模块：串口通信

#### 5. 配置加载模块
- **lensReadIni.cpp/h** - 原有配置加载
  - 功能：从INI和XML文件加载所有参数
  - 问题：与新配置系统不兼容

### 模块间配置依赖关系

```
光路调节主流程 (clenAdjustOperation)
├── 系统配置 (MES参数、设备配置)
├── 调节流程配置 (判定次数、固化时间、异常处理)
├── 光斑检测 (faculaCircle)
│   ├── 光斑坐标配置
│   ├── 检测阈值配置
│   └── 图像处理 (CFaculaContext)
│       ├── 插值配置
│       ├── 滤波器配置
│       └── 算法参数配置
└── 硬件控制 (clensMachine)
    ├── 设备类型配置
    ├── 步进电机配置
    └── 限位参数配置
```

## 配置加载机制分析

### 当前配置生成方式

#### 1. 代码生成机制 ✅
- **方式**: 程序运行时动态生成
- **触发条件**: 配置文件不存在时自动生成
- **生成位置**: `bin/config/moduleName/`
- **生成内容**: 包含默认值和详细注释的INI文件

#### 2. 配置文件结构
```
bin/config/moduleName/
├── system_config.ini          # 代码生成，包含默认值
├── algorithm_config.ini    # 代码生成，包含默认值
├── hardware_config.ini      # 代码生成，包含默认值
└── adjustprocess_config.ini  # 代码生成，包含默认值
```

#### 3. 配置加载流程
1. **程序启动** → 检查配置文件是否存在
2. **文件不存在** → 调用`setDefaults()`生成默认配置
3. **保存到文件** → 调用`saveConfig()`写入INI文件
4. **加载配置** → 调用`loadConfig()`从INI文件读取
5. **验证配置** → 调用`validate()`检查参数有效性

### 配置修改验证机制

#### 手动修改配置文件测试计划

**测试步骤**:
1. 修改`system_config.ini`中的MES参数
2. 修改`algorithm_config.ini`中的算法参数
3. 修改`hardware_config.ini`中的限位参数
4. 修改`adjustprocess_config.ini`中的流程参数
5. 重启程序验证参数是否正确加载

**验证方法**:
- 在程序日志中查看加载的参数值
- 通过调试输出确认参数生效
- 观察功能行为是否符合修改后的配置

## 重构问题总结与建议

### 🔍 实际代码使用情况分析

#### 1. **新配置系统使用情况**

**仅在以下位置使用**:
- `main.cpp` - 测试代码，仅用于验证配置系统
- `test/config/config_integration_test.cpp` - 集成测试
- `components/lensAdjust/lensReadIni.cpp` - 配置加载器（但未被实际调用）
- `sensor/photonSensor/faculaContext.cpp` - 部分使用新配置

#### 2. **旧配置系统仍在使用**

**核心业务代码仍使用`m_xml_param`**:
- `sensor/photonSensor/faculaCircle.cpp` - **55处使用`m_xml_param`**
  - `m_xml_param["find_origin_raduis"]` - 寻找光斑半径
  - `m_xml_param["initial_x_dist"]` - 初始X移动距离
  - `m_xml_param["peak_ok_threshold"]` - 光斑阈值
  - `m_xml_param["z_move_step"]` - Z轴移动步进
  - 等等...

**问题**: 这些核心参数在新配置系统中**完全缺失**！

#### 3. **配置参数缺失分析**

**新配置系统缺少的关键参数**:
```cpp
// faculaCircle.cpp中使用但新配置中缺失的参数
m_xml_param["find_origin_raduis"]     // 寻找光斑半径 - ❌ 缺失
m_xml_param["initial_x_dist"]         // 初始X距离 - ❌ 缺失
m_xml_param["initial_y_dist"]         // 初始Y距离 - ❌ 缺失
m_xml_param["initial_z_dist"]         // 初始Z距离 - ❌ 缺失
m_xml_param["find_times"]             // 寻找次数 - ❌ 缺失
m_xml_param["find_radius_step"]       // 半径步进 - ❌ 缺失
m_xml_param["find_angle_step"]        // 角度步进 - ❌ 缺失
m_xml_param["peak_ok_threshold"]      // 阈值 - ❌ 缺失
m_xml_param["z_move_step"]            // Z轴步进 - ❌ 缺失
m_xml_param["default_z_direct"]       // Z轴方向 - ❌ 缺失
m_xml_param["Amp_select"]             // 通道选择 - ❌ 缺失
m_xml_param["LR_peak_offset"]         // 左右偏移 - ❌ 缺失
m_xml_param["UD_peak_offset"]         // 上下偏移 - ❌ 缺失
// ... 还有40+个参数
```

**结论**: 新配置系统只迁移了不到20%的实际使用参数！

## 🎯 正确的重构方案

### 第一步：建立默认配置文件

#### 1. **在config目录建立默认配置**

**建议目录结构**:
```
F:\13_Yapha-Laser-DTof2dMS\development\tool\LA-T5\config\
├── default\                          # 默认配置模板
│   ├── system_config.ini             # 系统配置模板
│   ├── algorithm_config.ini          # 算法配置模板
│   ├── hardware_config.ini           # 硬件配置模板
│   ├── adjustprocess_config.ini      # 调节流程配置模板
│   └── facula_config.ini             # 光斑配置模板（新增）
```

#### 2. **配置部署机制**

```cpp
// 配置部署逻辑
class ConfigDeployer {
public:
    static bool deployDefaultConfigs(const QString& targetDir) {
        QString defaultConfigDir = "F:/13_Yapha-Laser-DTof2dMS/development/tool/LA-T5/config/default/";

        // 拷贝默认配置文件到目标目录
        QStringList configFiles = {
            "system_config.ini",
            "algorithm_config.ini",
            "hardware_config.ini",
            "adjustprocess_config.ini",
            "facula_config.ini"
        };

        for (const QString& file : configFiles) {
            QString source = defaultConfigDir + file;
            QString target = targetDir + file;

            if (!QFile::exists(target)) {
                QFile::copy(source, target);
            }
            
        }
        return true;
    }
};
```

### 第三步：代码迁移计划

#### 1. **修改faculaCircle.cpp**

**当前问题**: 55处使用`m_xml_param`，需要全部替换

**迁移方案**:
```cpp
// 在faculaCircle.h中添加配置成员
class CFaculaCircle {
private:
    // 替换 m_xml_param
    Config::FaculaConfigData* m_facula_config = nullptr;

public:
    // 在构造函数中初始化配置
    CFaculaCircle(const IFaculaAdjust::StMapInfo &st_map_info) {
        // 获取配置对象
        m_facula_config = Config::ConfigService::getInstance()
            .getDynamicManager()
            .getConfig<Config::FaculaConfigData>("Facula");

        if (!m_facula_config) {
            qCritical() << "Failed to load Facula config!";
            // 使用默认值或抛出异常
        }

        // 原有初始化逻辑
        m_find_radius = m_facula_config->getFindOriginRadius();
        // ...
    }
};
```

**参数替换示例**:
```cpp
// 替换前
move_3d_->x = m_xml_param["initial_x_dist"] / 7;
move_3d_->y = m_xml_param["initial_y_dist"] / 7;
move_3d_->z = m_xml_param["initial_z_dist"] / 8;

// 替换后
move_3d_->x = m_facula_config->getInitialXDist() / 7;
move_3d_->y = m_facula_config->getInitialYDist() / 7;
move_3d_->z = m_facula_config->getInitialZDist() / 8;
```

#### 2. **创建FaculaConfigData类**

```cpp
// sensor/photonSensor/config/FaculaConfigData.h
namespace Config {
class FaculaConfigData : public BaseConfigData<FaculaConfigData> {
public:
    static QString staticTypeName() { return "Facula"; }

    // Motion control parameters
    int getInitialXDist() const { return initial_x_dist; }
    int getInitialYDist() const { return initial_y_dist; }
    int getInitialZDist() const { return initial_z_dist; }
    int getDefaultZDirect() const { return default_z_direct; }
    int getZMoveStep() const { return z_move_step; }

    // Search parameters
    int getFindOriginRadius() const { return find_origin_raduis; }
    int getFindAngleStep() const { return find_angle_step; }
    int getFindRadiusStep() const { return find_radius_step; }
    int getFindTimes() const { return find_times; }

    // Threshold parameters
    int getPeakOkThreshold() const { return peak_ok_threshold; }
    int getPeakThreshold() const { return peak_threshold; }
    int getPeakMaxThreshold() const { return peak_max_threshold; }
    int getEdgePeakThreshold() const { return edge_peak_threshold; }

    // Symmetry parameters
    int getAmpSelect() const { return Amp_select; }
    int getLRPeakOffset() const { return LR_peak_offset; }
    int getUDPeakOffset() const { return UD_peak_offset; }
    // ... 更多getter方法

private:
    // Motion control
    int initial_x_dist = 0;
    int initial_y_dist = 0;
    int initial_z_dist = 0;
    int default_z_direct = 1;
    int z_move_step = 3;

    // Search parameters
    int find_origin_raduis = 140;
    int find_angle_step = 20;
    int find_radius_step = 140;
    int find_times = 4;

    // Threshold parameters
    int peak_ok_threshold = 550;
    int peak_threshold = 200;
    int peak_max_threshold = 4000;
    int edge_peak_threshold = 180;

    // Symmetry parameters
    int Amp_select = 30;
    int LR_peak_offset = 0;
    int UD_peak_offset = 0;
    // ... 更多参数
};
}
```

#### 3. **迁移优先级**

**第一阶段**: 核心参数迁移
- motion_control section (5个参数)
- search_parameters section (4个参数)
- threshold_parameters section (4个参数)

**第二阶段**: 对称性参数迁移
- symmetry_parameters section (15+个参数)

**第三阶段**: 验证和测试
- 单元测试验证
- 集成测试验证
- 功能回归测试

#### 1. **完整的配置模块划分**

建议的配置文件结构：
```
bin/config/lenAdjust/

```



## 编译 & 测试

### 构建配置
- **Build Type**: Debug
- **CMake Generator**: Ninja
- **编译器**: MinGW 7.3.0 (Qt 5.14.2)

### CMake配置
```bash
cmake -DCMAKE_BUILD_TYPE:STRING=Debug \
      -DCMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE \
      -DCMAKE_C_COMPILER:FILEPATH=D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-gcc.exe \
      -DCMAKE_CXX_COMPILER:FILEPATH=D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\x86_64-w64-mingw32-g++.exe \
      -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
      -S F:/13_Yapha-Laser-DTof2dMS/development/tool/LA-T5 \
      -B f:/13_Yapha-Laser-DTof2dMS/development/tool/LA-T5/build/Debug \
      -G Ninja
```

### 验证检查
- **配置加载验证**: 检查所有配置参数是否正确加载
- **日志检查**: 查看配置管理相关的日志输出
- **功能测试**: 验证光路调节功能是否正常工作
- **性能测试**: 确保重构后性能没有显著下降

## 预期输出

### 配置文件结构
- 模块化的配置目录结构
- JSON格式的配置文件
- 完整的配置模板和示例

### 代码架构
- 统一的配置管理接口
- 模块化的光路调节功能
- 清晰的职责分离和依赖关系

### 文档输出
- [[TOF接收镜片光路耦合软件使用文档]]
- 配置管理开发文档
- 模块化架构设计文档

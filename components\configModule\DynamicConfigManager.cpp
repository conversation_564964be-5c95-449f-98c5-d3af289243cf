#include "DynamicConfigManager.h"
#include <QApplication>
#include <QDateTime>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QSettings>
#include <QTextStream>

namespace Config {

DynamicConfigManager::DynamicConfigManager(QObject *parent) : QObject(parent), m_baseConfigDir(QApplication::applicationDirPath() + "/config/lenAdjust/") {
    // 确保基础配置目录存在
    QDir dir;
    if (!dir.exists(m_baseConfigDir)) {
        dir.mkpath(m_baseConfigDir);
        logInfo(QString("Created base config directory: %1").arg(m_baseConfigDir));
    }
}

DynamicConfigManager::~DynamicConfigManager() {
    clear();
}

bool DynamicConfigManager::registerConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    if (m_configs.find(typeName) != m_configs.end()) {
        logWarning(QString("Config '%1' already registered").arg(typeName));
        return false;
    }

    // 使用注册器创建配置对象
    auto config = ConfigTypeRegistry::getInstance().createConfig(typeName);
    if (!config) {
        logError(QString("Failed to create config '%1'").arg(typeName));
        return false;
    }

    // 设置默认值
    config->setDefaults();

    // 设置默认配置文件路径
    QString filePath            = m_baseConfigDir + typeName.toLower() + "_config.ini";
    m_configFilePaths[typeName] = filePath;

    m_configs[typeName] = std::move(config);

    logInfo(QString("Registered config: %1").arg(typeName));
    Q_EMIT configRegistered(typeName);

    return true;
}

bool DynamicConfigManager::registerConfig(std::unique_ptr<IConfigData> config) {
    if (!config) {
        logError("Cannot register null config");
        return false;
    }

    QString typeName = config->getTypeName();

    QMutexLocker locker(&m_mutex);

    if (m_configs.find(typeName) != m_configs.end()) {
        logWarning(QString("Config '%1' already registered").arg(typeName));
        return false;
    }

    // 设置默认配置文件路径
    QString filePath            = m_baseConfigDir + typeName.toLower() + "_config.ini";
    m_configFilePaths[typeName] = filePath;

    m_configs[typeName] = std::move(config);

    logInfo(QString("Registered config: %1").arg(typeName));
    Q_EMIT configRegistered(typeName);

    return true;
}

bool DynamicConfigManager::unregisterConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        logWarning(QString("Config '%1' not found for unregistration").arg(typeName));
        return false;
    }

    m_configs.erase(it);
    m_configFilePaths.erase(typeName);

    logInfo(QString("Unregistered config: %1").arg(typeName));
    Q_EMIT configUnregistered(typeName);

    return true;
}

IConfigData *DynamicConfigManager::getConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        logWarning(QString("Config '%1' not found").arg(typeName));
        return nullptr;
    }

    return it->second.get();
}

bool DynamicConfigManager::hasConfig(const QString &typeName) const {
    QMutexLocker locker(&m_mutex);
    return m_configs.find(typeName) != m_configs.end();
}

QStringList DynamicConfigManager::getAllConfigs() const {
    QMutexLocker locker(&m_mutex);

    QStringList configs;
    for (const auto &pair : m_configs) {
        configs.append(pair.first);
    }

    return configs;
}

ConfigResult DynamicConfigManager::loadConfig(const QString &typeName, const QString &filePath) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        QString msg = QString("Config '%1' not registered").arg(typeName);
        logError(msg);
        return ConfigResult(false, ErrorType::ModuleNotFound, msg);
    }

    QString configFile = filePath.isEmpty() ? getConfigFilePath(typeName) : filePath;

    ConfigResult result = loadConfigFromIni(it->second.get(), configFile);

    Q_EMIT configLoaded(typeName, result.success);

    if (result.success) {
        logInfo(QString("Loaded config: %1 from %2").arg(typeName, configFile));
        Q_EMIT configChanged(typeName);
    } else {
        logError(QString("Failed to load config: %1 - %2").arg(typeName, result.message));
    }

    return result;
}

ConfigResult DynamicConfigManager::saveConfig(const QString &typeName, const QString &filePath) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        QString msg = QString("Config '%1' not registered").arg(typeName);
        logError(msg);
        return ConfigResult(false, ErrorType::ModuleNotFound, msg);
    }

    QString configFile = filePath.isEmpty() ? getConfigFilePath(typeName) : filePath;

    ConfigResult result = saveConfigToIni(it->second.get(), configFile);

    Q_EMIT configSaved(typeName, result.success);

    if (result.success) {
        logInfo(QString("Saved config: %1 to %2").arg(typeName, configFile));
    } else {
        logError(QString("Failed to save config: %1 - %2").arg(typeName, result.message));
    }

    return result;
}

ConfigResult DynamicConfigManager::loadAllConfigs() {
    QStringList configs      = getAllConfigs();
    int         successCount = 0;
    QString     errorMessages;

    for (const QString &typeName : configs) {
        ConfigResult result = loadConfig(typeName);
        if (result.success) {
            successCount++;
        } else {
            errorMessages += QString("%1: %2; ").arg(typeName, result.message);
        }
    }

    bool    allSuccess = (successCount == configs.size());
    QString message    = QString("Loaded %1/%2 configs").arg(successCount).arg(configs.size());

    if (!allSuccess) {
        message += QString(". Errors: %1").arg(errorMessages);
    }

    logInfo(message);
    return ConfigResult(allSuccess, allSuccess ? ErrorType::None : ErrorType::UnknownError, message);
}

ConfigResult DynamicConfigManager::initializeAllConfigs() {
    QStringList configs      = getAllConfigs();
    int         successCount = 0;
    QString     errorMessages;

    logInfo(QString("Initializing %1 registered configs...").arg(configs.size()));

    for (const QString &typeName : configs) {
        QString configFile = getConfigFilePath(typeName);

        // 检查配置文件是否存在
        if (!QFile::exists(configFile)) {
            logInfo(QString("Config file not found for '%1', generating default: %2").arg(typeName, configFile));

            // 确保目录存在
            if (!ensureConfigDirectory(configFile)) {
                errorMessages += QString("%1: Failed to create directory; ").arg(typeName);
                continue;
            }

            // 生成默认配置并保存
            ConfigResult result = saveConfig(typeName, configFile);
            if (!result.success) {
                errorMessages += QString("%1: %2; ").arg(typeName, result.message);
                continue;
            }
        }

        // 加载配置
        ConfigResult result = loadConfig(typeName, configFile);
        if (result.success) {
            successCount++;
        } else {
            errorMessages += QString("%1: %2; ").arg(typeName, result.message);
        }
    }

    bool    allSuccess = (successCount == configs.size());
    QString message    = QString("Initialized %1/%2 configs").arg(successCount).arg(configs.size());

    if (!allSuccess) {
        message += QString(". Errors: %1").arg(errorMessages);
    }

    logInfo(message);
    return ConfigResult(allSuccess, allSuccess ? ErrorType::None : ErrorType::UnknownError, message);
}

ConfigResult DynamicConfigManager::saveAllConfigs() {
    QStringList configs      = getAllConfigs();
    int         successCount = 0;
    QString     errorMessages;

    for (const QString &typeName : configs) {
        ConfigResult result = saveConfig(typeName);
        if (result.success) {
            successCount++;
        } else {
            errorMessages += QString("%1: %2; ").arg(typeName, result.message);
        }
    }

    bool    allSuccess = (successCount == configs.size());
    QString message    = QString("Saved %1/%2 configs").arg(successCount).arg(configs.size());

    if (!allSuccess) {
        message += QString(". Errors: %1").arg(errorMessages);
    }

    logInfo(message);
    return ConfigResult(allSuccess, allSuccess ? ErrorType::None : ErrorType::UnknownError, message);
}

ConfigResult DynamicConfigManager::validateConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        QString msg = QString("Config '%1' not registered").arg(typeName);
        logError(msg);
        return ConfigResult(false, ErrorType::ModuleNotFound, msg);
    }

    bool    isValid = it->second->validate();
    QString message = isValid ? QString("Config '%1' is valid").arg(typeName) : QString("Config '%1' validation failed").arg(typeName);

    if (isValid) {
        logInfo(message);
    } else {
        logError(message);
    }

    return ConfigResult(isValid, isValid ? ErrorType::None : ErrorType::ValidationError, message);
}

ConfigResult DynamicConfigManager::validateAllConfigs() {
    QStringList configs    = getAllConfigs();
    int         validCount = 0;
    QString     errorMessages;

    for (const QString &typeName : configs) {
        ConfigResult result = validateConfig(typeName);
        if (result.success) {
            validCount++;
        } else {
            errorMessages += QString("%1: %2; ").arg(typeName, result.message);
        }
    }

    bool    allValid = (validCount == configs.size());
    QString message  = QString("Validated %1/%2 configs").arg(validCount).arg(configs.size());

    if (!allValid) {
        message += QString(". Errors: %1").arg(errorMessages);
    }

    logInfo(message);
    return ConfigResult(allValid, allValid ? ErrorType::None : ErrorType::ValidationError, message);
}

bool DynamicConfigManager::resetConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        logError(QString("Config '%1' not registered").arg(typeName));
        return false;
    }

    it->second->setDefaults();
    logInfo(QString("Reset config to defaults: %1").arg(typeName));
    Q_EMIT configChanged(typeName);

    return true;
}

bool DynamicConfigManager::resetAllConfigs() {
    QStringList configs    = getAllConfigs();
    bool        allSuccess = true;

    for (const QString &typeName : configs) {
        if (!resetConfig(typeName)) {
            allSuccess = false;
        }
    }

    logInfo(QString("Reset %1 configs to defaults").arg(configs.size()));
    return allSuccess;
}

void DynamicConfigManager::logInfo(const QString &message) const {
    qDebug() << "[DynamicConfigManager]" << message;
}

void DynamicConfigManager::logError(const QString &message) const {
    qCritical() << "[DynamicConfigManager] ERROR:" << message;
}

void DynamicConfigManager::logWarning(const QString &message) const {
    qWarning() << "[DynamicConfigManager] WARNING:" << message;
}

std::unique_ptr<IConfigData> DynamicConfigManager::cloneConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        logError(QString("Config '%1' not registered").arg(typeName));
        return nullptr;
    }

    return it->second->clone();
}

bool DynamicConfigManager::compareConfigs(const QString &typeName1, const QString &typeName2) {
    QMutexLocker locker(&m_mutex);

    auto it1 = m_configs.find(typeName1);
    auto it2 = m_configs.find(typeName2);

    if (it1 == m_configs.end() || it2 == m_configs.end()) {
        logError("One or both configs not found for comparison");
        return false;
    }

    return it1->second->equals(*it2->second);
}

QVariantMap DynamicConfigManager::getStatistics() const {
    QMutexLocker locker(&m_mutex);

    QVariantMap stats;
    stats["total_configs"]      = static_cast<int>(m_configs.size());
    stats["registered_configs"] = getAllConfigs();
    stats["base_config_dir"]    = m_baseConfigDir;

    QVariantMap configPaths;
    for (const auto &pair : m_configFilePaths) {
        configPaths[pair.first] = pair.second;
    }
    stats["config_file_paths"] = configPaths;

    return stats;
}

void DynamicConfigManager::clear() {
    QMutexLocker locker(&m_mutex);
    m_configs.clear();
    m_configFilePaths.clear();
    logInfo("Cleared all configs");
}

QString DynamicConfigManager::getConfigFilePath(const QString &typeName) const {
    auto it = m_configFilePaths.find(typeName);
    if (it != m_configFilePaths.end()) {
        return it->second;
    }

    // 返回默认路径
    return m_baseConfigDir + typeName.toLower() + "/" + typeName.toLower() + "_config.ini";
}

void DynamicConfigManager::setConfigFilePath(const QString &typeName, const QString &filePath) {
    QMutexLocker locker(&m_mutex);
    m_configFilePaths[typeName] = filePath;
    logInfo(QString("Set config file path for '%1': %2").arg(typeName, filePath));
}

bool DynamicConfigManager::ensureConfigDirectory(const QString &filePath) const {
    QFileInfo fileInfo(filePath);
    QDir      dir = fileInfo.absoluteDir();

    if (!dir.exists()) {
        if (!dir.mkpath(dir.absolutePath())) {
            logError(QString("Failed to create directory: %1").arg(dir.absolutePath()));
            return false;
        }
        logInfo(QString("Created directory: %1").arg(dir.absolutePath()));
    }

    return true;
}

ConfigResult DynamicConfigManager::loadConfigFromJson(IConfigData *config, const QString &filePath) {
    if (!QFile::exists(filePath)) {
        QString msg = QString("Config file not found: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileNotFound, msg);
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        QString msg = QString("Cannot open config file: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileReadError, msg);
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError parseError;
    QJsonDocument   doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        QString msg = QString("JSON parse error: %1").arg(parseError.errorString());
        return ConfigResult(false, ErrorType::ParseError, msg);
    }

    QVariantMap variantMap = doc.object().toVariantMap();

    if (!config->fromVariantMap(variantMap)) {
        QString msg = "Failed to load config from variant map";
        return ConfigResult(false, ErrorType::ParseError, msg);
    }

    return ConfigResult(true);
}

ConfigResult DynamicConfigManager::saveConfigToJson(const IConfigData *config, const QString &filePath) {
    if (!ensureConfigDirectory(filePath)) {
        QString msg = QString("Failed to create config directory for: %1").arg(filePath);
        return ConfigResult(false, ErrorType::PermissionError, msg);
    }

    QVariantMap   variantMap = config->toVariantMap();
    QJsonObject   jsonObj    = QJsonObject::fromVariantMap(variantMap);
    QJsonDocument doc(jsonObj);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        QString msg = QString("Cannot open config file for writing: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileWriteError, msg);
    }

    qint64 bytesWritten = file.write(doc.toJson());
    file.close();

    if (bytesWritten == -1) {
        QString msg = QString("Failed to write config file: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileWriteError, msg);
    }

    return ConfigResult(true);
}

ConfigResult DynamicConfigManager::saveConfigToJsonWithComments(const IConfigData *config, const QString &filePath) {
    if (!ensureConfigDirectory(filePath)) {
        QString msg = QString("Failed to create config directory for: %1").arg(filePath);
        return ConfigResult(false, ErrorType::PermissionError, msg);
    }

    QVariantMap variantMap       = config->toVariantMap();
    QString     jsonWithComments = generateJsonWithComments(config, variantMap);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        QString msg = QString("Cannot open config file for writing: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileWriteError, msg);
    }

    qint64 bytesWritten = file.write(jsonWithComments.toUtf8());
    file.close();

    if (bytesWritten == -1) {
        QString msg = QString("Failed to write config file: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileWriteError, msg);
    }

    return ConfigResult(true);
}

QString DynamicConfigManager::generateJsonWithComments(const IConfigData *config, const QVariantMap &data) {
    QString result = "{\n";

    // 添加文件头注释
    result += QString("    // %1 配置文件\n").arg(config->getTypeName());
    result += QString("    // 版本: %1\n").arg(config->getVersion());
    result += QString("    // 描述: %1\n").arg(config->getDescription());
    result += QString("    // 生成时间: %1\n").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    result += "\n";

    // 处理配置数据
    QStringList keys = data.keys();
    keys.sort();  // 按字母顺序排序

    // 过滤掉元数据字段
    QStringList filteredKeys;
    for (const QString &key : keys) {
        if (!key.startsWith("_")) {
            filteredKeys.append(key);
        }
    }

    for (int i = 0; i < filteredKeys.size(); ++i) {
        const QString & key   = filteredKeys[i];
        const QVariant &value = data[key];

        // 添加字段注释
        if (config->hasField(key)) {
            QString description = config->getFieldDescription(key);
            QString fieldType   = config->getFieldType(key);
            result += QString("    // %1 (%2)\n").arg(description, fieldType);
        }

        // 添加字段值
        result += QString("    \"%1\": ").arg(key);

        if (value.type() == QVariant::Map) {
            // 处理嵌套对象
            result += formatNestedObject(value.toMap(), 1);
        } else {
            result += formatValue(value);
        }

        // 添加逗号（除了最后一个元素）
        if (i < filteredKeys.size() - 1) {
            result += ",";
        }
        result += "\n";

        // 在字段之间添加空行
        if (i < filteredKeys.size() - 1) {
            result += "\n";
        }
    }

    result += "}\n";
    return result;
}

QString DynamicConfigManager::formatNestedObject(const QVariantMap &obj, int indentLevel) {
    QString indent = QString("    ").repeated(indentLevel);
    QString result = "{\n";

    QStringList keys = obj.keys();
    for (int i = 0; i < keys.size(); ++i) {
        const QString & key   = keys[i];
        const QVariant &value = obj[key];

        result += QString("%1    \"%2\": ").arg(indent, key);
        result += formatValue(value);

        if (i < keys.size() - 1) {
            result += ",";
        }
        result += "\n";
    }

    result += indent + "}";
    return result;
}

QString DynamicConfigManager::formatValue(const QVariant &value) {
    switch (value.type()) {
    case QVariant::String:
        return QString("\"%1\"").arg(value.toString());
    case QVariant::Int:
    case QVariant::UInt:
    case QVariant::LongLong:
    case QVariant::ULongLong:
        return QString::number(value.toLongLong());
    case QVariant::Double:
        return QString::number(value.toDouble());
    case QVariant::Bool:
        return value.toBool() ? "true" : "false";
    default:
        return QString("\"%1\"").arg(value.toString());
    }
}

ConfigResult DynamicConfigManager::loadConfigFromIni(IConfigData *config, const QString &filePath) {
    QSettings settings(filePath, QSettings::IniFormat);

    if (settings.status() != QSettings::NoError) {
        QString msg = QString("Cannot open INI config file for reading: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileReadError, msg);
    }

    try {
        QVariantMap variantMap;

        // 读取所有组和键值
        QStringList groups = settings.childGroups();

        // 如果没有组，读取根级别的键
        if (groups.isEmpty()) {
            QStringList keys = settings.allKeys();
            for (const QString &key : keys) {
                variantMap[key] = settings.value(key);
            }
        } else {
            // 读取各个组的数据
            for (const QString &group : groups) {
                settings.beginGroup(group);
                QVariantMap groupMap;
                QStringList keys = settings.allKeys();
                for (const QString &key : keys) {
                    groupMap[key] = settings.value(key);
                }
                settings.endGroup();
                variantMap[group] = groupMap;
            }
        }

        // 加载到配置对象
        bool success = config->fromVariantMap(variantMap);
        if (!success) {
            QString msg = QString("Failed to load config data from INI: %1").arg(filePath);
            return ConfigResult(false, ErrorType::ValidationError, msg);
        }

        return ConfigResult(true);

    } catch (const std::exception &e) {
        QString msg = QString("INI parse error: %1").arg(e.what());
        return ConfigResult(false, ErrorType::ValidationError, msg);
    }
}

QString DynamicConfigManager::generateIniString(const IConfigData *config, const QVariantMap &data) {
    QString result;

    // 添加文件头注释
    result += QString("; %1 配置文件\n").arg(config->getTypeName());
    result += QString("; 版本: %1\n").arg(config->getVersion());
    result += QString("; 描述: %1\n").arg(config->getDescription());
    result += QString("; 生成时间: %1\n").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    result += "\n";

    // 过滤掉元数据字段
    QStringList keys = data.keys();
    keys.sort();
    QStringList filteredKeys;
    for (const QString &key : keys) {
        if (!key.startsWith("_")) {
            filteredKeys.append(key);
        }
    }

    // 分离调节参数和判定参数
    QStringList adjustmentParams = {// 运动控制参数
                                    "initial_x_dist",
                                    "initial_y_dist",
                                    "initial_z_dist",
                                    "default_z_direct",
                                    "z_move_step",
                                    "discard_pack_num",
                                    // 搜索参数
                                    "find_origin_raduis",
                                    "find_angle_step",
                                    "find_radius_step",
                                    "find_times",
                                    "peak_ok_threshold",
                                    // 对称性调节参数
                                    "Amp_select",
                                    "ALR_mp_peak",
                                    "ALR_mp_peak_threshold",
                                    "AUD_mp_peak",
                                    "AUD_mp_peak_threshold",
                                    "Aedge_peak_threshold",
                                    "ACR_peak_delta",
                                    "ARR_peak_delta",
                                    "AMax_peak"};

    QStringList judgmentParams = {// 基础判定参数
                                  "edge_peak_threshold",
                                  "peak_threshold",
                                  "peak_max_threshold",
                                  "CR_peak_delta",
                                  // 微调判定参数
                                  "FT_LRmp_adjust_peak",
                                  "FT_LRmp_adjust_peak_threshold",
                                  "FT_UDmp_adjust_peak",
                                  "FT_UDmp_adjust_peak_threshold",
                                  "FT_LRmp_solid_peak",
                                  "FT_LRmp_solid_peak_threshold",
                                  "FT_UDmp_solid_peak",
                                  "FT_UDmp_solid_peak_threshold",
                                  "FT_LRmp_deflate_peak",
                                  "FT_LRmp_deflate_peak_threshold",
                                  "FT_UDmp_deflate_peak",
                                  "FT_UDmp_deflate_peak_threshold"};

    // 生成调节参数节
    result += "\n[AdjustmentParams]\n";
    result += "; 调节参数 - 用于光斑调节过程的控制参数\n\n";

    for (const QString &key : adjustmentParams) {
        if (filteredKeys.contains(key)) {
            const QVariant &value = data[key];
            if (config->hasField(key)) {
                QString description = config->getFieldDescription(key);
                QString fieldType   = config->getFieldType(key);
                if (!description.isEmpty()) {
                    result += QString("; %1").arg(description);
                    if (!fieldType.isEmpty()) {
                        result += QString(" (%1)").arg(fieldType);
                    }
                    result += "\n";
                }
            }
            result += QString("%1=%2\n").arg(key, variantToIniValue(value));
        }
    }

    // 生成判定参数节
    result += "\n[JudgmentParams]\n";
    result += "; 判定参数 - 用于光斑质量判定的阈值参数\n\n";

    for (const QString &key : judgmentParams) {
        if (filteredKeys.contains(key)) {
            const QVariant &value = data[key];
            if (config->hasField(key)) {
                QString description = config->getFieldDescription(key);
                QString fieldType   = config->getFieldType(key);
                if (!description.isEmpty()) {
                    result += QString("; %1").arg(description);
                    if (!fieldType.isEmpty()) {
                        result += QString(" (%1)").arg(fieldType);
                    }
                    result += "\n";
                }
            }
            result += QString("%1=%2\n").arg(key, variantToIniValue(value));
        }
    }

    // 处理其他未分类的参数
    QStringList otherParams;
    for (const QString &key : filteredKeys) {
        if (!adjustmentParams.contains(key) && !judgmentParams.contains(key)) {
            const QVariant &value = data[key];
            if (value.type() != QVariant::Map) {
                otherParams.append(key);
            }
        }
    }

    if (!otherParams.isEmpty()) {
        result += "\n[General]\n";
        result += "; 其他参数\n\n";

        for (const QString &key : otherParams) {
            const QVariant &value = data[key];
            if (config->hasField(key)) {
                QString description = config->getFieldDescription(key);
                QString fieldType   = config->getFieldType(key);
                if (!description.isEmpty()) {
                    result += QString("; %1").arg(description);
                    if (!fieldType.isEmpty()) {
                        result += QString(" (%1)").arg(fieldType);
                    }
                    result += "\n";
                }
            }
            result += QString("%1=%2\n").arg(key, variantToIniValue(value));
        }
    }

    // 处理分组字段
    for (const QString &key : filteredKeys) {
        const QVariant &value = data[key];
        if (value.type() == QVariant::Map) {
            result += QString("\n[%1]\n").arg(key);
            QVariantMap section     = value.toMap();
            QStringList sectionKeys = section.keys();
            sectionKeys.sort();

            for (const QString &sectionKey : sectionKeys) {
                QString fullFieldName = key + "." + sectionKey;
                if (config->hasField(sectionKey) || config->hasField(fullFieldName)) {
                    QString description = config->getFieldDescription(sectionKey);
                    if (description.isEmpty()) {
                        description = config->getFieldDescription(fullFieldName);
                    }
                    QString fieldType = config->getFieldType(sectionKey);
                    if (fieldType.isEmpty()) {
                        fieldType = config->getFieldType(fullFieldName);
                    }
                    if (!description.isEmpty()) {
                        result += QString("; %1").arg(description);
                        if (!fieldType.isEmpty()) {
                            result += QString(" (%1)").arg(fieldType);
                        }
                        result += "\n";
                    }
                }
                result += QString("%1=%2\n").arg(sectionKey, variantToIniValue(section[sectionKey]));
            }
        }
    }

    return result;
}

QString DynamicConfigManager::variantToIniValue(const QVariant &value) {
    switch (value.type()) {
    case QVariant::String:
        return value.toString();
    case QVariant::Int:
    case QVariant::UInt:
    case QVariant::LongLong:
    case QVariant::ULongLong:
        return QString::number(value.toLongLong());
    case QVariant::Double:
        return QString::number(value.toDouble());
    case QVariant::Bool:
        return value.toBool() ? "true" : "false";
    default:
        return value.toString();
    }
}

ConfigResult DynamicConfigManager::saveConfigToIni(const IConfigData *config, const QString &filePath) {
    if (!ensureConfigDirectory(filePath)) {
        QString msg = QString("Failed to create config directory for: %1").arg(filePath);
        return ConfigResult(false, ErrorType::PermissionError, msg);
    }

    QVariantMap variantMap = config->toVariantMap();
    QString     iniString  = generateIniString(config, variantMap);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        QString msg = QString("Cannot open config file for writing: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileWriteError, msg);
    }

    QTextStream stream(&file);
    stream.setCodec("UTF-8");
    stream << iniString;
    file.close();

    return ConfigResult(true);
}

void DynamicConfigManager::writeIniWithComments(QSettings &settings, const IConfigData *config, const QVariantMap &data) {
    // 这个方法用于更高级的INI写入，如果需要的话
    // 目前使用generateIniString方法来生成带注释的INI文件
    Q_UNUSED(settings)
    Q_UNUSED(config)
    Q_UNUSED(data)
}

}  // namespace Config

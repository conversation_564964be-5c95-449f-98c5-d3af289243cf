#pragma once

#include "../../components/configModule/BaseConfigProvider.h"

namespace AdjustProcess {

/**
 * @brief 调节流程配置提供者
 *
 * 专门负责光路调节流程控制相关的配置管理
 * 配置结构完全在模块内部定义，不依赖外部配置类型
 *
 * 职责：
 * - 管理光斑判定次数配置
 * - 管理固化时间配置
 * - 管理异常处理方式配置
 * - 处理调节流程相关的参数验证
 */
class AdjustProcessConfigProvider : public Config::BaseConfigProvider {
    Q_OBJECT

  public:
    explicit AdjustProcessConfigProvider(QObject *parent = nullptr);
    ~AdjustProcessConfigProvider() override = default;

    // IConfigProvider 接口实现
    QString getModuleName() const override {
        return "AdjustProcess";
    }
    QString getModuleVersion() const override {
        return "1.0.0";
    }
    QString getConfigFilePath() const override;

    /**
     * @brief 获取光斑判定次数
     * @return 判定次数
     */
    uint8_t getFaculaOkTimes() const;

    /**
     * @brief 设置光斑判定次数
     * @param times 判定次数
     */
    void setFaculaOkTimes(uint8_t times);

    /**
     * @brief 获取固化时间
     * @return 固化时间(毫秒)
     */
    uint32_t getSolidTime() const;

    /**
     * @brief 设置固化时间
     * @param time 固化时间(毫秒)
     */
    void setSolidTime(uint32_t time);

    /**
     * @brief 获取异常处理方式
     * @return 异常处理方式：0-停止，1-继续，2-重试
     */
    uint8_t getFaculaNgHandle() const;

    /**
     * @brief 设置异常处理方式
     * @param handle 异常处理方式：0-停止，1-继续，2-重试
     */
    void setFaculaNgHandle(uint8_t handle);

    /**
     * @brief 获取异常处理方式描述
     * @param handle 异常处理方式
     * @return 描述字符串
     */
    QString getFaculaNgHandleDescription(uint8_t handle) const;

    /**
     * @brief 获取当前异常处理方式描述
     * @return 描述字符串
     */
    QString getCurrentFaculaNgHandleDescription() const;

  protected:
    // BaseConfigProvider 接口实现
    void                 loadDefaultParameters() override;
    Config::ConfigResult validateParameter(const QString &key, const QVariant &value) const override;
    QString              getParameterRange(const QString &key) const override;
    void                 onParameterChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue) override;

  private:
    /**
     * @brief 验证光斑判定次数
     * @param times 判定次数
     * @return 是否有效
     */
    bool isValidFaculaOkTimes(uint8_t times) const;

    /**
     * @brief 验证固化时间
     * @param time 固化时间
     * @return 是否有效
     */
    bool isValidSolidTime(uint32_t time) const;

    /**
     * @brief 验证异常处理方式
     * @param handle 异常处理方式
     * @return 是否有效
     */
    bool isValidFaculaNgHandle(uint8_t handle) const;

  private:
    // 配置参数键名常量
    static const QString KEY_FACULA_OK_TIMES;
    static const QString KEY_SOLID_TIME;
    static const QString KEY_FACULA_NG_HANDLE;

    // 默认值常量
    static const uint8_t  DEFAULT_FACULA_OK_TIMES;
    static const uint32_t DEFAULT_SOLID_TIME;
    static const uint8_t  DEFAULT_FACULA_NG_HANDLE;

    // 验证范围常量
    static const uint8_t  MIN_FACULA_OK_TIMES;
    static const uint8_t  MAX_FACULA_OK_TIMES;
    static const uint32_t MAX_SOLID_TIME;
    static const uint8_t  MAX_FACULA_NG_HANDLE;

    // 异常处理方式枚举
    enum class NgHandleType : uint8_t {
        Stop     = 0,  // 停止
        Continue = 1,  // 继续
        Retry    = 2   // 重试
    };
};

}  // namespace AdjustProcess

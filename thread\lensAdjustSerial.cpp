/*******************************
 * sensor、镜片调节设备交互
 *******************/

#include "lensAdjustSerial.h"
#include <QEventLoop>
#include <QThread>


CLensAdjustSerial::CLensAdjustSerial(QObject *parent, ITopBoard *top_board_, IClensMachine *machine_)
    : QObject(parent), m_task_id(3), mc_top_board_(top_board_), mc_machine_(machine_) {
}

CLensAdjustSerial::~CLensAdjustSerial() {
    // if(mc_sensor_ != nullptr) delete mc_sensor_;
    // if(mc_machine_ != nullptr) delete mc_machine_;
}

void CLensAdjustSerial::loop(const bool &is_exc) {
    Q_UNUSED(is_exc);
    QByteArray arr;
    QByteArray device_arr;
    //    QEventLoop loop;
    for (;;) {
        //        if(!is_exc) {
        //            emit loopFinished();
        //            loop.quit();
        //            return;
        //        }
        m_mutex.lock();

        if (m_task_id == 1 && (mc_top_board_ != nullptr)) {  //接收sensor数据
            arr = mc_top_board_->portDataRead();
            if (arr.length() > 0)
                mc_top_board_->greyParsing(arr, arr.length());
        } else if (m_task_id == 2 && (mc_machine_ != nullptr)) {  //接收device数据
            device_arr = mc_machine_->portDataRead();
            if (device_arr.length() > 0)
                mc_machine_->dataParsing(device_arr, 100);
        } else if (m_task_id == 3 && (mc_top_board_ != nullptr)) {
            arr = mc_top_board_->portDataRead();
            //            uint8_t test_buff[] = {0xa5, 0x1a, 0xa3, 0x4d, 0x06, 0x00, 0x19, 0x41, 30 34 31 35 02 01 0x34 38 54 54};

            if (arr.length() > 0)
                mc_top_board_->interactionParsing(arr, 100);
        } else if (m_task_id == 4) {
            // QThread::msleep(1);
        } else if (m_task_id == 5) {  //空
            QThread::msleep(1);
        } else if (m_task_id == 0) {  //退出线程 //不能用else，时序上存在问题
            //            loop.quit();
            m_mutex.unlock();

            break;  // return
        }
        m_mutex.unlock();
        //        loop.processEvents(); // 处理事件
    }
}

void CLensAdjustSerial::device_change_interface(ITopBoard *top_board_, IClensMachine *clen_machine_) {
    m_mutex.lock();

    if (top_board_ != nullptr) {
        mc_top_board_ = top_board_;
        mc_top_board_->m_strPre.clear();
    }
    if (clen_machine_ != nullptr) {
        mc_machine_ = clen_machine_;
        mc_machine_->m_strPre.clear();
    }

    m_mutex.unlock();
}

void CLensAdjustSerial::task_id_change(const uint8_t &id) {
    m_mutex.lock();


    ////    if((mc_top_board_ != nullptr) && (mc_top_board_->m_strPre.length() > 0)) {
    ////        mc_top_board_->portDataRead();
    //////        mc_top_board_->m_strPre.clear();
    ////    }

    ////    qDebug() << "-i len thread/ task id:" << m_task_id;

    ////    if((mc_machine_ != nullptr) && (mc_machine_->m_strPre.length() > 0)) {
    ////        mc_machine_->portDataRead();
    //////        mc_machine_->m_strPre.clear();
    ////    }

    m_task_id = id;
    mc_top_board_->portDataRead();
    if (mc_machine_ != nullptr) {
        mc_machine_->portDataRead();
    }
    //    qDebug() << "-i len thread/ task id 1:" << m_task_id;

    m_mutex.unlock();
}

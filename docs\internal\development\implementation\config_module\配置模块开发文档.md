# 配置模块开发文档

## 模块概述

配置模块是激光工具系统的核心基础设施，负责管理所有模块的配置数据，包括算法参数、硬件设置、系统配置等。

### 模块职责

1. **配置数据管理** - 统一管理所有模块的配置参数
2. **配置持久化** - 支持配置的加载、保存和备份
3. **配置验证** - 确保配置数据的有效性和一致性
4. **配置服务** - 为其他模块提供配置访问接口
5. **动态扩展** - 支持运行时添加新的配置类型

### 设计原则

1. **开闭原则** - 对扩展开放，对修改关闭
2. **单一职责** - 每个类只负责一个配置方面
3. **依赖倒置** - 依赖抽象接口，不依赖具体实现
4. **工厂模式** - 使用工厂创建配置对象
5. **注册机制** - 支持动态注册新配置类型

## 架构设计

### 真正模块化架构 (2025年更新)

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        APP[主程序]
        UI[用户界面]
    end

    subgraph "Config模块 - 只提供基础设施"
        CS[ConfigService<br/>配置服务中心]
        DCM[DynamicConfigManager<br/>动态配置管理器]
        CTR[ConfigTypeRegistry<br/>类型注册器]
        ICD[IConfigData<br/>配置数据接口]
    end

    subgraph "PhotonSensor模块"
        FCD[FaculaConfigData<br/>光斑检测配置]
        PSM[PhotonSensorModule<br/>模块管理器]
        FD[FaculaDetection<br/>光斑检测算法]
    end

    subgraph "LensAdjust模块"
        ACD[AdjustProcessConfigData<br/>调节流程配置]
        LAM[LensAdjustModule<br/>模块管理器]
        CA[CLensAdjust<br/>调节控制]
    end

    subgraph "Algorithm模块"
        ALCD[AlgorithmConfigData<br/>算法配置]
        ALM[AlgorithmModule<br/>模块管理器]
        IP[ImageProcessing<br/>图像处理]
    end

    subgraph "Machine模块"
        HCD[HardwareConfigData<br/>硬件配置]
        MM[MachineModule<br/>模块管理器]
        MC[MotionControl<br/>运动控制]
    end

    APP --> CS
    UI --> CS

    CS --> DCM
    DCM --> CTR
    CTR --> ICD

    FCD --> ICD
    ACD --> ICD
    ALCD --> ICD
    HCD --> ICD

    PSM --> FCD
    LAM --> ACD
    ALM --> ALCD
    MM --> HCD

    FD --> FCD
    CA --> ACD
    IP --> ALCD
    MC --> HCD

    style CS fill:#e1f5fe
    style DCM fill:#f3e5f5
    style CTR fill:#e8f5e8
    style FCD fill:#fff3e0
    style ACD fill:#fff3e0
    style ALCD fill:#fff3e0
    style HCD fill:#fff3e0
```

### 🎯 **关键架构变更**

1. **Config模块职责明确** - 只提供基础设施，不包含具体配置
2. **各模块自己管理配置** - 配置数据类在各自模块中定义
3. **自动注册机制** - 使用REGISTER_CONFIG_TYPE宏自动注册
4. **统一访问接口** - 通过ConfigService提供统一访问

### 核心组件

#### 1. IConfigData - 配置数据接口
```cpp
class IConfigData {
public:
    virtual QString getTypeName() const = 0;
    virtual QVariantMap toVariantMap() const = 0;
    virtual bool fromVariantMap(const QVariantMap &data) = 0;
    virtual bool validate() const = 0;
    virtual void setDefaults() = 0;
    virtual std::unique_ptr<IConfigData> clone() const = 0;
    // ... 其他接口方法
};
```

#### 2. ConfigTypeRegistry - 类型注册器
```cpp
class ConfigTypeRegistry {
public:
    static ConfigTypeRegistry &getInstance();
    
    template<typename T>
    bool registerConfigType(const QString &typeName);
    
    std::unique_ptr<IConfigData> createConfig(const QString &typeName);
    QStringList getRegisteredTypes() const;
    // ... 其他方法
};
```

#### 3. DynamicConfigManager - 动态配置管理器
```cpp
class DynamicConfigManager : public QObject {
public:
    bool registerConfig(const QString &typeName);
    
    template<typename T>
    T* getConfig(const QString &typeName);
    
    ConfigResult loadConfig(const QString &typeName);
    ConfigResult saveConfig(const QString &typeName);
    // ... 其他方法
};
```

#### 4. ConfigService - 配置服务中心
```cpp
class ConfigService : public QObject {
public:
    static ConfigService &getInstance();
    
    bool registerProvider(std::unique_ptr<IConfigProvider> provider);
    IConfigProvider* getProvider(const QString &moduleName);
    
    ConfigResult loadAllConfigs();
    ConfigResult saveAllConfigs();
    // ... 其他方法
};
```

## 类图设计

```mermaid
classDiagram
    class IConfigData {
        <<interface>>
        +getTypeName() QString
        +toVariantMap() QVariantMap
        +fromVariantMap(QVariantMap) bool
        +validate() bool
        +setDefaults() void
        +clone() unique_ptr~IConfigData~
    }
    
    class ConfigTypeRegistry {
        <<singleton>>
        -s_factories: map~QString, function~
        +registerConfigType~T~(QString) bool
        +createConfig(QString) unique_ptr~IConfigData~
        +getRegisteredTypes() QStringList
    }
    
    class DynamicConfigManager {
        -m_configs: map~QString, unique_ptr~IConfigData~~
        +registerConfig(QString) bool
        +getConfig~T~(QString) T*
        +loadConfig(QString) ConfigResult
        +saveConfig(QString) ConfigResult
    }
    
    class ConfigService {
        <<singleton>>
        -m_dynamicManager: DynamicConfigManager
        -m_providers: map~QString, unique_ptr~IConfigProvider~~
        +registerProvider(unique_ptr~IConfigProvider~) bool
        +getProvider(QString) IConfigProvider*
        +loadAllConfigs() ConfigResult
    }
    
    class AlgorithmConfigData {
        +parameters: QMap~QString, int~
        +interpolation_type: uint8_t
        +filter_types: QString
        +filter_strength: float
        +getParameter(QString, int) int
        +setParameter(QString, int) void
    }
    
    IConfigData <|-- AlgorithmConfigData
    ConfigTypeRegistry --> IConfigData
    DynamicConfigManager --> IConfigData
    DynamicConfigManager --> ConfigTypeRegistry
    ConfigService --> DynamicConfigManager
```

## 配置类型定义

### 当前支持的配置类型

1. **AlgorithmConfig** - 算法配置
   - 传统算法参数 (60+ 参数)
   - 图像处理参数
   - 插值和滤波器设置

2. **FaculaConfig** - 光斑配置
   - 光斑检测参数
   - 光斑处理设置
   - 光斑调节参数

3. **SystemConfig** - 系统配置
   - MES集成设置
   - 版本管理
   - 系统级参数

4. **HardwareConfig** - 硬件配置
   - 设备通信参数
   - 硬件控制设置

### 配置文件格式

- **存储格式**: JSON
- **文件位置**: `config/modules/{module_name}/{module_name}_config.json`
- **编码格式**: UTF-8
- **版本控制**: 配置文件包含版本信息

## 配置模块工作流程

### 完整工作流程图

```mermaid
flowchart TD
    A[系统启动] --> B[ConfigService初始化]
    B --> C[扫描注册的配置类型]
    C --> D{配置文件是否存在?}

    D -->|不存在| E[生成默认配置文件]
    D -->|存在| F[验证配置文件格式]
    E --> F
    F --> G{配置文件有效?}

    G -->|无效| H[使用默认配置并重新生成文件]
    G -->|有效| I[加载配置到内存]
    H --> I

    I --> J[配置系统就绪]
    J --> K[模块请求配置]
    K --> L[ConfigService返回配置对象]
    L --> M[模块读取/修改参数]
    M --> N{需要保存?}

    N -->|是| O[保存配置到文件]
    N -->|否| P[继续使用]
    O --> P
    P --> Q{系统继续运行?}
    Q -->|是| K
    Q -->|否| R[系统退出]

    style A fill:#e1f5fe
    style J fill:#c8e6c9
    style R fill:#ffcdd2
```

### 详细流程说明

#### 1. 模块定义阶段
```cpp
// 在lensAdjust模块中定义配置类
class AdjustProcessConfigData : public Config::BaseConfigData<AdjustProcessConfigData> {
public:
    static QString staticTypeName() { return "AdjustProcess"; }

    // 配置字段
    int facula_ok_times = 3;
    int solid_time = 0;
    int facula_ng_handle = 1;

    // 实现接口方法...
};

// 自动注册（在模块加载时执行）
REGISTER_CONFIG_TYPE(AdjustProcessConfigData, "AdjustProcess", "1.0.0", "调节流程配置");
```

#### 2. 系统启动阶段
```cpp
// 主程序启动时
int main() {
    // ConfigService自动初始化
    ConfigService& service = ConfigService::getInstance();

    // 检查并生成配置文件
    service.initializeAllConfigs();  // 这会：
    // - 扫描所有注册的配置类型
    // - 检查 config/modules/lensAdjust/adjust_process_config.json 是否存在
    // - 不存在则调用 AdjustProcessConfigData::setDefaults() 生成默认配置
    // - 保存为JSON格式文件
}
```

#### 3. 模块运行阶段
```cpp
// 在lensAdjust模块中使用配置
void CLensAdjust::startAdjustment() {
    // 获取配置对象
    auto* config = ConfigService::getInstance()
        .getDynamicManager()
        .getConfig<AdjustProcessConfigData>("AdjustProcess");

    if (config) {
        // 读取参数
        int okTimes = config->getFaculaOkTimes();
        int solidTime = config->getSolidTime();

        // 使用参数执行业务逻辑
        performAdjustment(okTimes, solidTime);

        // 如果需要修改参数
        config->setFaculaOkTimes(5);

        // 保存配置
        ConfigService::getInstance().getDynamicManager().saveConfig("AdjustProcess");
    }
}
```

#### 4. 配置文件结构
```
config/
├── system/
│   └── system_config.json          # 系统级配置
└── modules/
    ├── lensAdjust/
    │   ├── adjust_process_config.json   # 调节流程配置
    │   └── facula_config.json          # 光斑配置
    ├── algorithm/
    │   └── algorithm_config.json       # 算法配置
    └── hardware/
        └── hardware_config.json        # 硬件配置
```

## 使用指南

### 添加新配置类型

1. **创建配置数据类**
```cpp
class NewModuleConfigData : public BaseConfigData<NewModuleConfigData> {
public:
    static QString staticTypeName() { return "NewModule"; }
    
    // 实现IConfigData接口
    QString getTypeName() const override;
    QVariantMap toVariantMap() const override;
    bool fromVariantMap(const QVariantMap &data) override;
    bool validate() const override;
    void setDefaults() override;
    
    // 配置字段
    QString someParameter;
    int anotherParameter;
};

// 自动注册
REGISTER_CONFIG_TYPE(NewModuleConfigData, "NewModule", "1.0.0", "新模块配置");
```

2. **创建配置提供者**
```cpp
class NewModuleConfigProvider : public BaseConfigProvider {
public:
    NewModuleConfigProvider() : BaseConfigProvider("NewModule") {}
    
    // 实现具体的加载/保存逻辑
};
```

3. **注册到服务**
```cpp
// 在模块初始化时
ConfigService::getInstance().registerProvider(
    std::make_unique<NewModuleConfigProvider>()
);
```

### 配置访问架构设计

#### 问题分析

传统的配置访问方式存在以下问题：
1. **参数重复声明** - 业务类中重复定义配置参数结构体
2. **违反DRY原则** - 同一参数在多个地方定义
3. **代码冗长** - 每个参数都需要单独的getter调用
4. **维护困难** - 新增参数需要修改多个地方

#### 推荐的配置访问架构

```mermaid
graph TB
    subgraph "配置层 (Configuration Layer)"
        FCD[FaculaConfigData<br/>配置数据类]
        DCM[DynamicConfigManager<br/>配置管理器]
        CF[facula_config.ini<br/>配置文件]
    end

    subgraph "业务层 (Business Layer)"
        FC[CFaculaCircle<br/>业务类]
        CA[ConfigAccessor<br/>配置访问器]
    end

    subgraph "访问模式对比"
        subgraph "❌ 旧模式 (不推荐)"
            LCP[LocalConfigParams<br/>本地参数结构体]
            LOAD[loadConfigParams()<br/>37行参数复制代码]
        end

        subgraph "✅ 新模式 (推荐)"
            REF[配置引用<br/>const FaculaConfigData*]
            LAZY[延迟加载<br/>getConfig()]
        end
    end

    CF --> DCM
    DCM --> FCD
    FCD --> CA
    CA --> FC

    FC -.-> LCP
    LCP -.-> LOAD

    FC --> REF
    REF --> LAZY

    style LCP fill:#ffcdd2
    style LOAD fill:#ffcdd2
    style REF fill:#c8e6c9
    style LAZY fill:#c8e6c9
```

#### 推荐实现模式

**方案：配置引用 + 延迟加载**

```cpp
class CFaculaCircle {
private:
    // 配置引用，延迟加载
    mutable const PhotonSensor::FaculaConfigData* m_faculaConfig = nullptr;

    // 安全的配置访问方法
    const PhotonSensor::FaculaConfigData* getConfig() const {
        if (!m_faculaConfig) {
            Config::ConfigService& service = Config::ConfigService::getInstance();
            m_faculaConfig = service.getDynamicManager()
                .getConfig<PhotonSensor::FaculaConfigData>("Facula");
        }
        return m_faculaConfig;
    }

public:
    // 支持配置热更新
    void refreshConfig() {
        m_faculaConfig = nullptr; // 强制重新加载
    }

    // 业务方法直接使用配置
    void adjustFacula() {
        auto* config = getConfig();
        if (config) {
            // 直接使用配置参数，无需本地缓存
            int xDist = config->getInitialXDist();
            int yDist = config->getInitialYDist();
            int threshold = config->getPeakThreshold();

            // 执行业务逻辑...
        }
    }
};
```

#### 架构优势

1. **消除重复** - 不再需要LocalConfigParams结构体
2. **支持热更新** - 配置变更可以实时生效
3. **延迟加载** - 按需获取配置，提高性能
4. **代码简洁** - 减少90%的配置加载代码
5. **符合原则** - 遵循单一职责和DRY原则

#### 迁移指南

1. **移除本地参数结构体**
   ```cpp
   // 删除这些代码
   struct LocalConfigParams { ... };
   LocalConfigParams m_config_params;
   void setDefaultConfigParams();
   void loadConfigParams();
   ```

2. **添加配置访问器**
   ```cpp
   // 添加这些代码
   mutable const ConfigDataType* m_config = nullptr;
   const ConfigDataType* getConfig() const;
   void refreshConfig();
   ```

3. **更新使用方式**
   ```cpp
   // 旧方式
   int value = m_config_params.some_parameter;

   // 新方式
   auto* config = getConfig();
   int value = config ? config->getSomeParameter() : defaultValue;
   ```

### 访问配置数据

```cpp
// 获取配置对象
auto* config = ConfigService::getInstance()
    .getDynamicManager()
    .getConfig<AlgorithmConfigData>("Algorithm");

if (config) {
    // 读取参数
    int threshold = config->getParameter("facula_threshold_min");

    // 设置参数
    config->setParameter("gaussian_sigma", 3);

    // 保存配置
    ConfigService::getInstance().getDynamicManager().saveConfig("Algorithm");
}
```

## 测试方案

### 主程序内嵌测试

使用CMakeLists.txt宏控制测试编译：

```cmake
option(ENABLE_CONFIG_TESTS "Enable configuration module tests" OFF)
option(ENABLE_CONFIG_MOCK_SIGNALS "Enable configuration mock signal tests" OFF)
option(ENABLE_CONFIG_VALIDATION "Enable configuration validation tests" OFF)
```

### 测试内容

1. **配置类型注册测试** - 验证动态注册机制
2. **配置管理器测试** - 验证配置的CRUD操作
3. **配置序列化测试** - 验证JSON序列化功能
4. **配置验证测试** - 验证数据有效性检查
5. **模拟信号测试** - 验证配置变更通知

### 测试触发方式

- 主程序启动后自动触发
- 使用QTimer模拟信号延迟执行
- 通过qDebug输出详细测试日志

## 性能考虑

1. **内存管理** - 使用智能指针管理配置对象生命周期
2. **线程安全** - 所有配置操作都是线程安全的
3. **延迟加载** - 配置对象按需创建和加载
4. **缓存机制** - 避免重复的文件I/O操作

## 扩展性

1. **插件化支持** - 支持动态加载配置插件
2. **配置模板** - 支持配置模板和继承
3. **配置迁移** - 支持配置版本升级和迁移
4. **远程配置** - 预留远程配置管理接口

## 维护指南

1. **日志记录** - 所有配置操作都有详细日志
2. **错误处理** - 完善的错误处理和恢复机制
3. **配置备份** - 自动备份重要配置文件
4. **版本兼容** - 向后兼容旧版本配置格式

配置模块采用现代C++设计模式，完全符合SOLID原则，为系统提供了强大而灵活的配置管理能力。

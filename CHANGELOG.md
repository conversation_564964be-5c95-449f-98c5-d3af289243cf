
## [v1.4.4] - 2025-07-21
- fix(config): add missing config files in Debug build directory

## [v1.4.3] - 2025-07-08
- docs(config): readd  config files in MinSizeRel to git

## [v1.4.2] - 2025-07-08
- docs(config): add config to git

## [v1.4.1] - 2025-05-09
- fix(debug): fix execute program pwd in launch.json
- fix(log): fix date repeats problem in output messagge

## [v1.4.0] - 2025-01-02
- feat(log): optimize log output function

## [v1.3.0] - 2024-10-15

- chore(generate_log): master commit msg add to every component CHANGELOG file
- feat(docs): user manual to assitant

## [v1.2.7] - 2024-09-13

- chore: remove unusing tasks
- refactor: add clang-format
- fix: remove multi-define COMM_ACK
- chore: add script files using for check commit and generate changelog、set executable file name
- fix: save log to error log file only in release build type

## [v1.2.6] - 2024-06-19

- refactor: sensor切换模式需要先解锁
- refactor: 改变手动XY轴移动方向

## [v0.0.3] - 2024-08-05

- chore(tasks): remove unusing tasks
- refactor(format): add clang-format
- fix: remove multi-define COMM_ACK
- fix: save log to error log file only in release build type

## [v0.0.2] - 2024-07-31

- docs(git): generate  changeLog and build after git commit
- docs(script): add script files using for check commit and generate changelog、set executable file name

## [v0.0.1] - 2024-07-31

- docs(tasks_json): add commit-release task, get commit msg from cur_msg.txt
- docs(script): add script files using for check commit and generate changelog、set executable file name

#include "AlgorithmConfigData.h"
#include <QDebug>

// 自动注册配置类型
REGISTER_CONFIG_TYPE(PhotonSensor::AlgorithmConfigData, "Algorithm", "1.0.0", "算法配置，包含图像处理和算法计算参数")

namespace PhotonSensor {

// 静态字段类型映射表
const QMap<QString, QString> AlgorithmConfigData::s_fieldTypes = {{"interpolation_type", "int"},
                                                                  {"filter_types", "int"},
                                                                  {"interpolation_offset", "float"},
                                                                  {"filter_strength", "float"},
                                                                  {"kalman_strength", "float"},
                                                                  {"convolution_kernel_size", "int"},
                                                                  {"convolution_preset", "string"},
                                                                  {"median_kernel_size", "int"},
                                                                  {"median_preset", "string"},
                                                                  {"gaussian_sigma", "float"},
                                                                  {"gaussian_kernel_size", "int"},
                                                                  {"gaussian_preset", "string"},
                                                                  {"bilateral_sigma_color", "float"},
                                                                  {"bilateral_sigma_space", "float"},
                                                                  {"bilateral_kernel_size", "int"},
                                                                  {"bilateral_preset", "string"},
                                                                  {"weighted_avg_kernel_size", "int"},
                                                                  {"weighted_avg_preset", "string"}};

// 静态字段描述映射表
const QMap<QString, QString> AlgorithmConfigData::s_fieldDescriptions = {
    {"interpolation_type", "插值类型：0-最近邻，1-双线性，2-双三次"},
    {"filter_types", "滤波器类型组合（位掩码）"},
    {"interpolation_offset", "插值偏移量"},
    {"filter_strength", "全局滤波强度"},
    {"kalman_strength", "卡尔曼滤波强度"},
    {"convolution_kernel_size", "卷积核大小"},
    {"convolution_preset", "卷积预设：sharpen-锐化，blur-模糊，edge-边缘检测"},
    {"median_kernel_size", "中值滤波核大小"},
    {"median_preset", "中值滤波预设：noise_reduction-降噪，preserve_edges-保留边缘"},
    {"gaussian_sigma", "高斯滤波标准差"},
    {"gaussian_kernel_size", "高斯滤波核大小"},
    {"gaussian_preset", "高斯滤波预设：light_blur-轻度模糊，medium_blur-中度模糊，heavy_blur-重度模糊"},
    {"bilateral_sigma_color", "双边滤波颜色标准差"},
    {"bilateral_sigma_space", "双边滤波空间标准差"},
    {"bilateral_kernel_size", "双边滤波核大小"},
    {"bilateral_preset", "双边滤波预设：smooth-平滑，edge_preserve-保留边缘"},
    {"weighted_avg_kernel_size", "加权均值滤波核大小"},
    {"weighted_avg_preset", "加权均值滤波预设：center_weighted-中心加权，uniform-均匀加权"}};

AlgorithmConfigData::AlgorithmConfigData() {
    setDefaults();
}

void AlgorithmConfigData::setDefaults() {
    // 图像处理参数
    interpolation_type   = 0;     // 默认使用最近邻插值
    filter_types         = 6;     // 默认使用高斯和双边滤波
    interpolation_offset = 0.5f;  // 默认插值偏移量
    filter_strength      = 1.0f;  // 默认滤波强度
    kalman_strength      = 1.0f;  // 默认卡尔曼滤波强度

    // 卷积滤波器参数
    convolution_kernel_size = 3;          // 默认3x3卷积核
    convolution_preset      = "sharpen";  // 默认锐化预设

    // 中值滤波器参数
    median_kernel_size = 3;                  // 默认3x3中值滤波核
    median_preset      = "noise_reduction";  // 默认降噪预设

    // 高斯滤波器参数
    gaussian_sigma       = 1.0f;           // 默认高斯标准差
    gaussian_kernel_size = 5;              // 默认5x5高斯核
    gaussian_preset      = "medium_blur";  // 默认中度模糊预设

    // 双边滤波器参数
    bilateral_sigma_color = 75.0f;     // 默认颜色标准差
    bilateral_sigma_space = 75.0f;     // 默认空间标准差
    bilateral_kernel_size = 5;         // 默认5x5双边滤波核
    bilateral_preset      = "smooth";  // 默认平滑预设

    // 加权均值滤波器参数
    weighted_avg_kernel_size = 3;                  // 默认3x3加权均值核
    weighted_avg_preset      = "center_weighted";  // 默认中心加权预设
}

QVariantMap AlgorithmConfigData::toVariantMap() const {
    QVariantMap result;

    // 图像处理参数
    result["interpolation_type"]   = interpolation_type;
    result["filter_types"]         = filter_types;
    result["interpolation_offset"] = interpolation_offset;
    result["filter_strength"]      = filter_strength;
    result["kalman_strength"]      = kalman_strength;

    // 卷积滤波器参数
    result["convolution_kernel_size"] = convolution_kernel_size;
    result["convolution_preset"]      = convolution_preset;

    // 中值滤波器参数
    result["median_kernel_size"] = median_kernel_size;
    result["median_preset"]      = median_preset;

    // 高斯滤波器参数
    result["gaussian_sigma"]       = gaussian_sigma;
    result["gaussian_kernel_size"] = gaussian_kernel_size;
    result["gaussian_preset"]      = gaussian_preset;

    // 双边滤波器参数
    result["bilateral_sigma_color"] = bilateral_sigma_color;
    result["bilateral_sigma_space"] = bilateral_sigma_space;
    result["bilateral_kernel_size"] = bilateral_kernel_size;
    result["bilateral_preset"]      = bilateral_preset;

    // 加权均值滤波器参数
    result["weighted_avg_kernel_size"] = weighted_avg_kernel_size;
    result["weighted_avg_preset"]      = weighted_avg_preset;

    return result;
}

bool AlgorithmConfigData::fromVariantMap(const QVariantMap &data) {
    bool success = true;

    // 图像处理参数
    if (data.contains("interpolation_type")) {
        interpolation_type = data["interpolation_type"].toInt();
    }
    if (data.contains("filter_types")) {
        filter_types = data["filter_types"].toInt();
    }
    if (data.contains("interpolation_offset")) {
        interpolation_offset = data["interpolation_offset"].toFloat();
    }
    if (data.contains("filter_strength")) {
        filter_strength = data["filter_strength"].toFloat();
    }
    if (data.contains("kalman_strength")) {
        kalman_strength = data["kalman_strength"].toFloat();
    }

    // 卷积滤波器参数
    if (data.contains("convolution_kernel_size")) {
        convolution_kernel_size = data["convolution_kernel_size"].toInt();
    }
    if (data.contains("convolution_preset")) {
        convolution_preset = data["convolution_preset"].toString();
    }

    // 中值滤波器参数
    if (data.contains("median_kernel_size")) {
        median_kernel_size = data["median_kernel_size"].toInt();
    }
    if (data.contains("median_preset")) {
        median_preset = data["median_preset"].toString();
    }

    // 高斯滤波器参数
    if (data.contains("gaussian_sigma")) {
        gaussian_sigma = data["gaussian_sigma"].toFloat();
    }
    if (data.contains("gaussian_kernel_size")) {
        gaussian_kernel_size = data["gaussian_kernel_size"].toInt();
    }
    if (data.contains("gaussian_preset")) {
        gaussian_preset = data["gaussian_preset"].toString();
    }

    // 双边滤波器参数
    if (data.contains("bilateral_sigma_color")) {
        bilateral_sigma_color = data["bilateral_sigma_color"].toFloat();
    }
    if (data.contains("bilateral_sigma_space")) {
        bilateral_sigma_space = data["bilateral_sigma_space"].toFloat();
    }
    if (data.contains("bilateral_kernel_size")) {
        bilateral_kernel_size = data["bilateral_kernel_size"].toInt();
    }
    if (data.contains("bilateral_preset")) {
        bilateral_preset = data["bilateral_preset"].toString();
    }

    // 加权均值滤波器参数
    if (data.contains("weighted_avg_kernel_size")) {
        weighted_avg_kernel_size = data["weighted_avg_kernel_size"].toInt();
    }
    if (data.contains("weighted_avg_preset")) {
        weighted_avg_preset = data["weighted_avg_preset"].toString();
    }

    return success && validate();
}

bool AlgorithmConfigData::validate() const {
    // 验证插值类型
    if (interpolation_type < 0 || interpolation_type > 2) {
        qWarning() << "Invalid interpolation_type:" << interpolation_type;
        return false;
    }

    // 验证滤波器核大小（必须是奇数）
    if (convolution_kernel_size % 2 == 0 || convolution_kernel_size < 3) {
        qWarning() << "Invalid convolution_kernel_size:" << convolution_kernel_size;
        return false;
    }
    if (median_kernel_size % 2 == 0 || median_kernel_size < 3) {
        qWarning() << "Invalid median_kernel_size:" << median_kernel_size;
        return false;
    }
    if (gaussian_kernel_size % 2 == 0 || gaussian_kernel_size < 3) {
        qWarning() << "Invalid gaussian_kernel_size:" << gaussian_kernel_size;
        return false;
    }
    if (bilateral_kernel_size % 2 == 0 || bilateral_kernel_size < 3) {
        qWarning() << "Invalid bilateral_kernel_size:" << bilateral_kernel_size;
        return false;
    }
    if (weighted_avg_kernel_size % 2 == 0 || weighted_avg_kernel_size < 3) {
        qWarning() << "Invalid weighted_avg_kernel_size:" << weighted_avg_kernel_size;
        return false;
    }

    // 验证标准差参数
    if (gaussian_sigma <= 0.0f) {
        qWarning() << "Invalid gaussian_sigma:" << gaussian_sigma;
        return false;
    }
    if (bilateral_sigma_color <= 0.0f) {
        qWarning() << "Invalid bilateral_sigma_color:" << bilateral_sigma_color;
        return false;
    }
    if (bilateral_sigma_space <= 0.0f) {
        qWarning() << "Invalid bilateral_sigma_space:" << bilateral_sigma_space;
        return false;
    }

    return true;
}

QStringList AlgorithmConfigData::getFieldNames() const {
    return s_fieldTypes.keys();
}

QString AlgorithmConfigData::getFieldType(const QString &fieldName) const {
    return s_fieldTypes.value(fieldName, QString());
}

QString AlgorithmConfigData::getFieldDescription(const QString &fieldName) const {
    return s_fieldDescriptions.value(fieldName, QString());
}

bool AlgorithmConfigData::hasField(const QString &fieldName) const {
    return s_fieldTypes.contains(fieldName);
}

QVariant AlgorithmConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    QVariantMap data = toVariantMap();
    return data.value(fieldName, defaultValue);
}

bool AlgorithmConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    QVariantMap data;
    data[fieldName] = value;
    return fromVariantMap(data);
}

bool AlgorithmConfigData::resetField(const QString &fieldName) {
    if (!hasField(fieldName)) {
        return false;
    }

    // 重置为默认值
    AlgorithmConfigData defaultConfig;
    QVariantMap         defaultData = defaultConfig.toVariantMap();

    if (defaultData.contains(fieldName)) {
        return setFieldValue(fieldName, defaultData[fieldName]);
    }

    return false;
}

}  // namespace PhotonSensor

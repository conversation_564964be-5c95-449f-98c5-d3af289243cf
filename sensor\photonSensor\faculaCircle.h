#ifndef _FACUAL_CIRCLE_H_
#define _FACULA_CIRCLE_H_

#include <QByteArray>

#include "IFaculaAdjust.h"
#include "qLog.h"

// 前向声明
namespace PhotonSensor {
class FaculaConfigData;
}

class CFaculaCircle : public IFaculaAdjust {

  public:
    CFaculaCircle(const IFaculaAdjust::StMapInfo &st_map_info);
    ~CFaculaCircle();


    void variblesInit() override;

    //    void targetMapUpdate(const StMapTargetInfo &target_map) override;
    //    int8_t faculaAdjust(StMapData *map_data_,
    //                        const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_) override;

    bool faculaMp(StMapData *map_data_) override;

    uint8_t faculaAdjust(StMapData *map_data_, const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_) override;

    uint16_t faculaTest(StMapData *map_data_, QVector<uint32_t> &target_facula, const EFaculaJudgeMode &test_mode, const bool &has_adjusted) override;

    void findMaxMP(StMapData *map_data_) override;

  private:
    enum class EAdjustStep {
        eDEFAULT_MOVE_STEP = 0,
        eRANDOM_FIND_STEP  = 1,
        eCENTRAL_STEP      = 2,
        eSYMMETRY_STEP     = 3,
        ePEAK_STEP         = 4,
    };

    enum class EZFindFunction {
        eMAX_PEAK = 1,
        eDISPERSE = 2,
    };

    typedef struct {
        uint16_t theta;
        uint16_t find_radius;
        uint8_t  find_facula_cnt;
    } StRandomFaculaInfo;

    typedef struct {
        uint32_t          max_peak;
        uint16_t          cross_aver_peak;
        QVector<uint32_t> edge_corner;
    } StZFaculaInfo;

    typedef struct {
        int16_t           max_around_peak;
        QVector<uint32_t> edge_corner;
        uint16_t          max_peak;
    } StZDisperseFacula;

    typedef struct {
        StSingleMp left;
        StSingleMp right;
        StSingleMp up;
        StSingleMp down;
        StSingleMp center;  //目标MP TF
    } StRoundJudgeMP;

    typedef struct {
        bool                               sensor_direction;     //贴片方向
        bool                               is_center_to_target;  // true: 先中心后目标点
        uint8_t                            map_xlen;
        uint8_t                            map_ylen;
        StRoundJudgeMP                     center_round;                  //中心区域
        StRoundJudgeMP                     target_round;                  //目标区域(中心与四周MP)
        QVector<IFaculaAdjust::StSingleMp> target_channels;               //多通道目标列表
        uint32_t                           multi_channel_peak_threshold;  //多通道peak阈值
        ESymmetryAdjustType                symm_type;
    } StTargetInfo;

    // 添加组件日志成员
    static const QMap<EAdjustStep, QString> mm_adjust_step_describe;

    IFaculaAdjust::UFaculaAdjustDds mu_facula_adjust;
    IFaculaAdjust::UFaculaJudgeDds  mu_facula_detect_items;

    EAdjustStep    m_adjust_len_step;
    EZFindFunction m_z_function_select;


    //* central find
    int32_t m_last_max_peak;
    int32_t m_z_peak_stable_cnt;
    //    C3dHandMachine::St3D<int16_t>         mst_max_move_delta;

    uint32_t m_adjust_timeout_num;  // 调节超时计时
    uint32_t m_adjust_timeout_cnt;

    uint16_t m_find_radius;

    StTargetInfo mst_target_info;

    // 预加载的配置参数（解决ConfigService实例问题）
    struct PreloadedConfig {
        // 基础参数
        int find_radius         = 140;
        int Amp_select          = 30;
        int AMax_peak           = 650;
        int peak_threshold      = 600;
        int peak_max_threshold  = 1000;
        int edge_peak_threshold = 50;
        int CR_peak_delta       = 150;

        // 查找参数
        int find_times         = 4;
        int find_radius_step   = 140;
        int find_angle_step    = 20;
        int find_origin_raduis = 140;

        // 峰值参数
        int peak_ok_threshold = 550;
        int ARR_peak_delta    = 40;
        int LR_peak_offset    = 0;
        int UD_peak_offset    = 0;

        // Z轴参数
        int default_z_direct = 1;
        int z_move_step      = 3;
        int initial_z_dist   = 0;

        // 分散光斑参数
        int ACR_peak_delta       = 120;
        int Aedge_peak_threshold = 180;
    } m_preloadedConfig;

    // 配置刷新方法（新架构）
    void refreshConfig();
    void preloadConfigParams();  // 预加载配置参数

    void                          faculaAdjustInfoHandle();
    bool                          targetFaculaMoveInfo(const IFaculaAdjust::StMapInfo &map_info, StTargetInfo *target_info_);  // override;
    C3dHandMachine::St3D<int16_t> getSymmetryPeakThreshold(const uint32_t &center_peak, const EFaculaJudgeMode &mode);
    void                          updateRoundMpPeak(StRoundJudgeMP *round_mp_, const QVector<QVector<uint32_t>> &map_matrix, const bool &has_adjusted);
    CFaculaCircle::StSymmetryInfo
    getSymmetryDelta(StRoundJudgeMP *round_mp_, const QVector<QVector<uint32_t>> &map_matrix, const StSymmetryInfo &mode, const bool &is_move_center);


    EFaculaStatus defaultFaculaMove(C3dHandMachine::St3D<int16_t> *move_3d_);
    EFaculaStatus
    randomFaculaFind(StRandomFaculaInfo *random_find_, const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_3d_);
    EFaculaStatus targetMove();
    EFaculaStatus centralMove(const C3dHandMachine::St3D<int16_t> &central_mp_delta, C3dHandMachine::St3D<int16_t> *move_3d_);
    EFaculaStatus
    symmetryMove(const C3dHandMachine::St3D<int16_t> &symmetry_mp_delta, const int16_t &xy_peak_delta_tor, C3dHandMachine::St3D<int16_t> *move_3d_);
    EFaculaStatus peakMove(int8_t &                             z_direction,
                           const StZFaculaInfo &                z_facula_info,
                           const EZFindFunction &               find_function,
                           C3dHandMachine::St3D<int16_t> *      move_3d_,
                           const C3dHandMachine::St3D<int16_t> &move_delta_step);

    EFaculaStatus zMaxPeakSingleStep(int8_t &                             z_direction,
                                     const StZFaculaInfo &                z_facula_info,
                                     const C3dHandMachine::St3D<int16_t> &move_delta_step,
                                     C3dHandMachine::St3D<int16_t> *      move_step_);
    EFaculaStatus zFindDisperseFacula(const StZDisperseFacula &z_disperse_info, C3dHandMachine::St3D<int16_t> *move_step_);
};


#endif

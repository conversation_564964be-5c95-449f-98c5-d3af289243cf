#include "clenEnterDataMes.h"
#include "qLog.h"
#include <QApplication>
#include <QDebug>
#include <QStringList>

// 添加配置系统支持
#include "ConfigInitializer.h"
#include "ConfigService.h"
#include "DynamicConfigManager.h"

// 强制引用配置类型以确保自动注册
#include "../../machine/3dHandMachine/config/HardwareConfigData.h"
#include "../../sensor/photonSensor/config/AlgorithmConfigData.h"
#include "../../sensor/photonSensor/config/FaculaConfigData.h"
#include "../configModule/SystemConfigData.h"
#include "../lensAdjust/config/AdjustProcessConfigData.h"


int main(int argc, char *argv[]) {
    QApplication a(argc, argv);

    MyLogger::QLog::init();
    MyLogger::QLog::installMessageHandler();

    LOG_INFO(MyLogger::LogType::INIT, "lenAdjust_MEMD application starting...");

    // 强制引用配置类型以确保自动注册（必须在配置初始化之前）
    {
        auto systemConfig    = std::make_unique<Config::SystemConfigData>();
        auto faculaConfig    = std::make_unique<PhotonSensor::FaculaConfigData>();
        auto algorithmConfig = std::make_unique<PhotonSensor::AlgorithmConfigData>();
        auto hardwareConfig  = std::make_unique<Machine::HardwareConfigData>();
        auto adjustConfig    = std::make_unique<LensAdjust::AdjustProcessConfigData>();

        // 强制引用静态方法
        (void)systemConfig->staticTypeName();
        (void)faculaConfig->staticTypeName();
        (void)algorithmConfig->staticTypeName();
        (void)hardwareConfig->staticTypeName();
        (void)adjustConfig->staticTypeName();
    }

    // 手动注册配置到DynamicConfigManager
    Config::ConfigService &       configService  = Config::ConfigService::getInstance();
    Config::DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 注册配置类型（使用实际的配置对象）
    dynamicManager.registerConfig(std::make_unique<Config::SystemConfigData>());
    dynamicManager.registerConfig(std::make_unique<PhotonSensor::FaculaConfigData>());
    dynamicManager.registerConfig(std::make_unique<PhotonSensor::AlgorithmConfigData>());
    dynamicManager.registerConfig(std::make_unique<Machine::HardwareConfigData>());
    dynamicManager.registerConfig(std::make_unique<LensAdjust::AdjustProcessConfigData>());

    // 初始化配置系统
    Config::ConfigInitializer configInit;
    auto                      result = configInit.initializeConfigs();
    if (result.success) {
        LOG_INFO(MyLogger::LogType::INIT, "Configuration system initialized successfully");
    } else {
        LOG_ERROR(MyLogger::LogType::INIT, QString("Failed to initialize configuration system: %1").arg(result.message));
        return -1;
    }

    clenEnterDataMes w;
    LOG_INFO(MyLogger::LogType::INIT, "lenAdjust_MEMD started");

    w.show();
    return a.exec();
}

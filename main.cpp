#include "logindialog.h"
#include "mywidget.h"
#include <QApplication>
#include <QStringList>
#include <iostream>

#include "qLog.h"

// 配置模块测试相关头文件
#ifdef ENABLE_CONFIG_TESTS
#include "algorithm/config/AlgorithmConfigData.h"
#include "components/configModule/ConfigService.h"
#include "components/configModule/DynamicConfigManager.h"
#include "components/configModule/SystemConfigData.h"
#include "components/lensAdjust/config/AdjustProcessConfigData.h"
#include "machine/config/HardwareConfigData.h"
#include "sensor/photonSensor/config/FaculaConfigData.h"
#include <QDebug>
#include <QTimer>


#endif

// #ifdef ENABLE_CONFIG_TESTS
// /**
//  * @brief 配置模块测试类
//  *
//  * 在主程序中内嵌的配置模块测试，使用模拟信号触发测试
//  */
// class ConfigModuleTest : public QObject {
//     Q_OBJECT

//   public:
//     explicit ConfigModuleTest(QObject *parent = nullptr) : QObject(parent) {
//     }

//   public slots:
//     /**
//      * @brief 测试配置类型注册
//      */
//     void testConfigRegistration() {
//         qDebug() << "========================================";
//         qDebug() << "开始测试配置类型注册";
//         qDebug() << "========================================";

//         // 测试配置类型注册器
//         auto &registry = Config::ConfigTypeRegistry::getInstance();

//         // 检查算法配置是否已注册
//         bool isRegistered = registry.isRegistered("Algorithm");
//         qDebug() << "算法配置注册状态:" << (isRegistered ? "已注册" : "未注册");

//         if (isRegistered) {
//             // 创建配置实例
//             auto config = registry.createConfig("Algorithm");
//             if (config) {
//                 qDebug() << "✅ 成功创建算法配置实例";
//                 qDebug() << "配置类型:" << config->getTypeName();
//                 qDebug() << "配置版本:" << config->getVersion();
//                 qDebug() << "配置描述:" << config->getDescription();
//             } else {
//                 qDebug() << "❌ 创建算法配置实例失败";
//             }
//         }

//         // 获取所有注册的类型
//         QStringList types = registry.getRegisteredTypes();
//         qDebug() << "已注册的配置类型:" << types;

//         qDebug() << "配置类型注册测试完成\n";

//         // 延迟触发下一个测试
//         QTimer::singleShot(1000, this, &ConfigModuleTest::testDynamicConfigManager);
//     }

//     /**
//      * @brief 测试动态配置管理器
//      */
//     void testDynamicConfigManager() {
//         qDebug() << "========================================";
//         qDebug() << "开始测试动态配置管理器";
//         qDebug() << "========================================";

//         // 创建动态配置管理器
//         Config::DynamicConfigManager manager;

//         // 注册算法配置
//         bool registered = manager.registerConfig("Algorithm");
//         qDebug() << "算法配置注册结果:" << (registered ? "成功" : "失败");

//         if (registered) {
//             // 获取配置对象
//             auto *algorithmConfig = manager.getConfig<Algorithm::AlgorithmConfigData>("Algorithm");
//             if (algorithmConfig) {
//                 qDebug() << "✅ 成功获取算法配置对象";

//                 // 测试参数设置
//                 algorithmConfig->setParameter("facula_threshold_min", 75);
//                 algorithmConfig->setParameter("gaussian_sigma", 3);
//                 algorithmConfig->setInterpolationType(2);
//                 algorithmConfig->setFilterStrength(2.5f);

//                 qDebug() << "设置参数: facula_threshold_min = 75";
//                 qDebug() << "设置参数: gaussian_sigma = 3";
//                 qDebug() << "设置插值类型: 2 (双三次)";
//                 qDebug() << "设置滤波器强度: 2.5";

//                 // 验证参数
//                 int     threshold      = algorithmConfig->getParameter("facula_threshold_min");
//                 int     sigma          = algorithmConfig->getParameter("gaussian_sigma");
//                 uint8_t interpType     = algorithmConfig->getInterpolationType();
//                 float   filterStrength = algorithmConfig->getFilterStrength();

//                 qDebug() << "读取参数: facula_threshold_min =" << threshold;
//                 qDebug() << "读取参数: gaussian_sigma =" << sigma;
//                 qDebug() << "读取插值类型:" << interpType;
//                 qDebug() << "读取滤波器强度:" << filterStrength;

//                 // 测试配置验证
//                 bool isValid = algorithmConfig->validate();
//                 qDebug() << "配置验证结果:" << (isValid ? "有效" : "无效");

//             } else {
//                 qDebug() << "❌ 获取算法配置对象失败";
//             }
//         }

//         qDebug() << "动态配置管理器测试完成\n";

//         // 延迟触发下一个测试
//         QTimer::singleShot(1000, this, &ConfigModuleTest::testConfigSerialization);
//     }

//     /**
//      * @brief 测试配置序列化
//      */
//     void testConfigSerialization() {
//         qDebug() << "========================================";
//         qDebug() << "开始测试配置序列化";
//         qDebug() << "========================================";

//         // 创建算法配置实例
//         Algorithm::AlgorithmConfigData config;

//         // 设置一些测试参数
//         config.setParameter("facula_threshold_min", 100);
//         config.setParameter("facula_threshold_max", 180);
//         config.setParameter("gaussian_kernel_size", 7);
//         config.setInterpolationType(1);
//         config.setFilterTypes("1,3,5");
//         config.setFilterStrength(3.0f);

//         // 测试转换为QVariantMap
//         QVariantMap variantMap = config.toVariantMap();
//         qDebug() << "✅ 成功转换为QVariantMap";
//         qDebug() << "参数数量:" << variantMap["parameters"].toMap().size();
//         qDebug() << "插值类型:" << variantMap["interpolation_type"].toUInt();
//         qDebug() << "滤波器类型:" << variantMap["filter_types"].toString();

//         // 测试从QVariantMap加载
//         Algorithm::AlgorithmConfigData newConfig;
//         bool                           loaded = newConfig.fromVariantMap(variantMap);
//         qDebug() << "从QVariantMap加载结果:" << (loaded ? "成功" : "失败");

//         if (loaded) {
//             // 验证数据一致性
//             bool consistent = (newConfig.getParameter("facula_threshold_min") == 100 && newConfig.getParameter("gaussian_kernel_size") == 7 &&
//                                newConfig.getInterpolationType() == 1 && newConfig.getFilterTypes() == "1,3,5");

//             qDebug() << "数据一致性检查:" << (consistent ? "通过" : "失败");
//         }

//         // 测试JSON序列化
//         QString jsonString = config.toJsonString();
//         qDebug() << "✅ 成功转换为JSON字符串";
//         qDebug() << "JSON长度:" << jsonString.length() << "字符";

//         qDebug() << "配置序列化测试完成\n";

//         // 延迟触发下一个测试
//         QTimer::singleShot(1000, this, &ConfigModuleTest::testAllModuleConfigs);
//     }

//     /**
//      * @brief 测试所有模块配置
//      */
//     void testAllModuleConfigs() {
//         qDebug() << "========================================";
//         qDebug() << "开始测试所有模块配置";
//         qDebug() << "========================================";

//         // 测试光子传感器配置
//         testFaculaConfig();

//         // 测试镜头调节配置
//         testAdjustProcessConfig();

//         // 测试硬件配置
//         testHardwareConfig();

//         qDebug() << "========================================";
//         qDebug() << "所有配置模块测试完成！";
//         qDebug() << "========================================";

//         // 自动生成配置文件
//         qDebug() << "\n========================================";
//         qDebug() << "开始生成配置文件";
//         qDebug() << "========================================";

//         Config::DynamicConfigManager configManager;

//         // 手动注册System配置类型（确保注册成功）
//         bool systemRegistered = Config::ConfigTypeRegistry::getInstance().registerConfigType<Config::SystemConfigData>(
//             "System", "1.0.0", "系统级配置，包含版本信息、MES系统配置和设备信息");
//         qDebug() << QString("手动注册System配置: %1").arg(systemRegistered ? "成功" : "失败");

//         // 注册所有可用的配置类型
//         QStringList configTypes = {"System", "Algorithm", "AdjustProcess", "Hardware"};
//         for (const QString &configType : configTypes) {
//             bool registered = configManager.registerConfig(configType);
//             if (registered) {
//                 qDebug() << QString("✅ 成功注册配置类型: %1").arg(configType);
//             } else {
//                 qDebug() << QString("❌ 注册配置类型失败: %1").arg(configType);
//             }
//         }

//         // 初始化所有配置（如果配置文件不存在，会生成默认配置文件）
//         Config::ConfigResult initResult = configManager.initializeAllConfigs();
//         if (initResult.success) {
//             qDebug() << "✅ 所有配置文件初始化成功";
//         } else {
//             qDebug() << QString("❌ 配置文件初始化失败: %1").arg(initResult.message);
//         }

//         // 保存所有配置到文件
//         Config::ConfigResult saveResult = configManager.saveAllConfigs();
//         if (saveResult.success) {
//             qDebug() << "✅ 所有配置文件保存成功";
//         } else {
//             qDebug() << QString("❌ 配置文件保存失败: %1").arg(saveResult.message);
//         }

//         qDebug() << "配置文件生成完成";
//         qDebug() << "========================================";
//     }

//   private:
//     void testFaculaConfig() {
//         qDebug() << "--- 测试光子传感器配置 ---";

//         // 首先检查Facula配置类型是否已注册
//         Config::ConfigTypeRegistry &registry       = Config::ConfigTypeRegistry::getInstance();
//         bool                        typeRegistered = registry.isRegistered("Facula");
//         qDebug() << "Facula配置类型注册状态:" << (typeRegistered ? "已注册" : "未注册");

//         // 显示所有已注册的类型进行调试
//         QStringList allTypes = registry.getRegisteredTypes();
//         qDebug() << "当前所有已注册的配置类型:" << allTypes;

//         // 强制引用FaculaConfigData以确保链接
//         PhotonSensor::FaculaConfigData *dummyFacula = nullptr;
//         Q_UNUSED(dummyFacula);

//         if (!typeRegistered) {
//             qDebug() << "❌ Facula配置类型未注册，跳过测试";
//             return;
//         }

//         // 创建动态配置管理器
//         Config::DynamicConfigManager manager;

//         // 注册光斑配置
//         bool registered = manager.registerConfig("Facula");
//         qDebug() << "光斑配置注册结果:" << (registered ? "成功" : "失败");

//         if (registered) {
//             auto *faculaConfig = manager.getConfig<PhotonSensor::FaculaConfigData>("Facula");
//             if (faculaConfig) {
//                 qDebug() << "✅ 成功获取光斑配置对象";

//                 // 测试多通道配置
//                 faculaConfig->setFaculaCenterChannels("3,3;5,5;7,7");
//                 faculaConfig->setFaculaCenterPeakThreshold(1000);
//                 faculaConfig->setFaculaHandleType(2);

//                 qDebug() << "设置多通道配置: 3,3;5,5;7,7";
//                 qDebug() << "设置Peak阈值: 1000";
//                 qDebug() << "设置处理类型: 2 (高精度)";

//                 // 验证配置
//                 QString  channels  = faculaConfig->getFaculaCenterChannels();
//                 uint32_t threshold = faculaConfig->getFaculaCenterPeakThreshold();
//                 QString  typeDesc  = faculaConfig->getFaculaHandleTypeDescription();

//                 qDebug() << "读取通道配置:" << channels;
//                 qDebug() << "读取Peak阈值:" << threshold;
//                 qDebug() << "读取处理类型:" << typeDesc;

//                 bool isValid = faculaConfig->validate();
//                 qDebug() << "光斑配置验证结果:" << (isValid ? "有效" : "无效");
//             }
//         }
//     }

//     void testAdjustProcessConfig() {
//         qDebug() << "--- 测试镜头调节配置 ---";

//         Config::DynamicConfigManager manager;
//         bool                         registered = manager.registerConfig("AdjustProcess");
//         qDebug() << "调节流程配置注册结果:" << (registered ? "成功" : "失败");

//         if (registered) {
//             auto *adjustConfig = manager.getConfig<LensAdjust::AdjustProcessConfigData>("AdjustProcess");
//             if (adjustConfig) {
//                 qDebug() << "✅ 成功获取调节流程配置对象";

//                 // 测试流程参数
//                 adjustConfig->setFaculaOkTimes(5);
//                 adjustConfig->setSolidTime(2000);
//                 adjustConfig->setFaculaNgHandle(2);

//                 qDebug() << "设置光斑判定次数: 5";
//                 qDebug() << "设置固化时间: 2000ms";
//                 qDebug() << "设置异常处理: 2 (重试)";

//                 // 验证配置
//                 uint8_t  okTimes   = adjustConfig->getFaculaOkTimes();
//                 uint32_t solidTime = adjustConfig->getSolidTime();
//                 QString  ngDesc    = adjustConfig->getFaculaNgHandleDescription();

//                 qDebug() << "读取判定次数:" << okTimes;
//                 qDebug() << "读取固化时间:" << solidTime;
//                 qDebug() << "读取异常处理:" << ngDesc;

//                 bool isValid = adjustConfig->validate();
//                 qDebug() << "调节流程配置验证结果:" << (isValid ? "有效" : "无效");
//             }
//         }
//     }

//     void testHardwareConfig() {
//         qDebug() << "--- 测试硬件配置 ---";

//         Config::DynamicConfigManager manager;
//         bool                         registered = manager.registerConfig("Hardware");
//         qDebug() << "硬件配置注册结果:" << (registered ? "成功" : "失败");

//         if (registered) {
//             auto *hardwareConfig = manager.getConfig<Machine::HardwareConfigData>("Hardware");
//             if (hardwareConfig) {
//                 qDebug() << "✅ 成功获取硬件配置对象";

//                 // 测试硬件参数
//                 hardwareConfig->setXYRadiusLimit(2000);
//                 hardwareConfig->setZRadiusLimit(1800);
//                 hardwareConfig->setXStepDist(5);
//                 hardwareConfig->setYStepDist(5);
//                 hardwareConfig->setZStepDist(8);

//                 qDebug() << "设置XY限位半径: 2000";
//                 qDebug() << "设置Z轴限位: 1800";
//                 qDebug() << "设置步进距离: X=5, Y=5, Z=8";

//                 // 验证配置
//                 uint32_t xyLimit = hardwareConfig->getXYRadiusLimit();
//                 uint32_t zLimit  = hardwareConfig->getZRadiusLimit();
//                 uint8_t  xStep   = hardwareConfig->getXStepDist();

//                 qDebug() << "读取XY限位:" << xyLimit;
//                 qDebug() << "读取Z限位:" << zLimit;
//                 qDebug() << "读取X步进:" << xStep;

//                 // 测试位置检查
//                 bool inLimit = hardwareConfig->isPositionInXYLimit(1000, 1000);
//                 qDebug() << "位置(1000,1000)是否在限位内:" << (inLimit ? "是" : "否");

//                 bool isValid = hardwareConfig->validate();
//                 qDebug() << "硬件配置验证结果:" << (isValid ? "有效" : "无效");
//             }
//         }

//         // Config::DynamicConfigManager manager;
//         // bool                         registered = manager.registerConfig("Hardware");
//         // qDebug() << "硬件配置注册结果:" << (registered ? "成功" : "失败");

//         // if (registered) {
//         //     auto *hardwareConfig = manager.getConfig<Machine::HardwareConfigData>("Hardware");
//         //     if (hardwareConfig) {
//         //         qDebug() << "✅ 成功获取硬件配置对象";

//         //         // 测试硬件参数
//         //         hardwareConfig->setXYRadiusLimit(2000);
//         //         hardwareConfig->setZRadiusLimit(1800);
//         //         hardwareConfig->setXStepDist(5);
//         //         hardwareConfig->setYStepDist(5);
//         //         hardwareConfig->setZStepDist(8);

//         //         qDebug() << "设置XY限位半径: 2000";
//         //         qDebug() << "设置Z轴限位: 1800";
//         //         qDebug() << "设置步进距离: X=5, Y=5, Z=8";

//         //         // 验证配置
//         //         uint32_t xyLimit = hardwareConfig->getXYRadiusLimit();
//         //         uint32_t zLimit  = hardwareConfig->getZRadiusLimit();
//         //         uint8_t  xStep   = hardwareConfig->getXStepDist();

//         //         qDebug() << "读取XY限位:" << xyLimit;
//         //         qDebug() << "读取Z限位:" << zLimit;
//         //         qDebug() << "读取X步进:" << xStep;

//         //         // 测试位置检查
//         //         bool inLimit = hardwareConfig->isPositionInXYLimit(1000, 1000);
//         //         qDebug() << "位置(1000,1000)是否在限位内:" << (inLimit ? "是" : "否");

//         //         bool isValid = hardwareConfig->validate();
//         //         qDebug() << "硬件配置验证结果:" << (isValid ? "有效" : "无效");
//         //     }
//         // }
//     }
// };

// #include "main.moc"  // 包含MOC生成的文件
// #endif               // ENABLE_CONFIG_TESTS

int main(int argc, char *argv[]) {
    QApplication a(argc, argv);
    myWidget     w;
    loginDialog  lgdl;

    // 强制引用SystemConfigData以确保自动注册生效
    (void)Config::SystemConfigData::staticTypeName();

    // 设置自定义的消息处理函数
    MyLogger::QLog::init();
    // MyLogger::QLog::setLogPath("logs/");

    MyLogger::QLog::installMessageHandler();

    // std::cout << "\033[31mThis is red text\033[0m" << std::endl;    // 红色文字
    // std::cout << "\033[32mThis is green text\033[0m" << std::endl;  // 绿色文字

    // qDebug() << "\033[31m"
    //          << "This is red text"
    //          << "\033[0m";  // 红色文字
    // qInfo() << "\033[32m"
    //         << "This is green text"
    //         << "\033[0m";  // 绿色文字
    // qWarning() << "\033[33m"
    //            << "This is yellow text"
    //            << "\033[0m";  // 黄色文字
    // qCritical() << "\033[31m"
    //             << "This is critical text"
    //             << "\033[0m";  // 红色文字

    // 使用日志宏
    LOG_INFO(MyLogger::LogType::INIT, "Application initialized");

#ifdef ENABLE_CONFIG_MOCK_SIGNALS
    qDebug() << "配置模块模拟信号测试已启用";
#endif

#ifdef ENABLE_CONFIG_VALIDATION
    qDebug() << "配置模块验证测试已启用";
#endif

    //    if(lgdl.exec() == QDialog::Accepted)
    //    {
    w.show();
    return a.exec();
    //    }
    //    else
    //        return 0;
}

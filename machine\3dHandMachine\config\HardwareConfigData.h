#pragma once

#include "../../../components/configModule/ConfigTypeRegistry.h"
#include "../../../components/configModule/IConfigData.h"
#include <QString>
#include <QVariantMap>

namespace Machine {

/**
 * @brief 硬件配置数据
 *
 * 管理硬件设备的所有配置参数：
 * - 步进电机参数（XYZ轴）
 * - 限位参数
 * - 运动控制参数
 *
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在machine模块中定义和管理
 * - 专门用于硬件设备控制
 * - 支持多种机器类型的配置
 */
class HardwareConfigData : public Config::BaseConfigData<HardwareConfigData> {
  public:
    HardwareConfigData();
    ~HardwareConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() {
        return "Hardware";
    }

    // IConfigData接口实现
    QString getTypeName() const override {
        return staticTypeName();
    }
    QString getVersion() const override {
        return "1.0.0";
    }
    QString getDescription() const override {
        return "硬件配置，包含步进电机和限位参数";
    }

    QVariantMap toVariantMap() const override;
    bool        fromVariantMap(const QVariantMap &data) override;
    bool        validate() const override;
    void        setDefaults() override;

    QStringList getFieldNames() const override;
    QString     getFieldType(const QString &fieldName) const override;
    QString     getFieldDescription(const QString &fieldName) const override;
    bool        hasField(const QString &fieldName) const override;
    QVariant    getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool        setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool        resetField(const QString &fieldName) override;

    // 限位参数访问接口
    /**
     * @brief 获取XY轴限位半径
     * @return XY轴限位半径 (微米)
     */
    float getXYRadiusLimit() const {
        return xy_radius_limit;
    }

    /**
     * @brief 设置XY轴限位半径
     * @param limit XY轴限位半径 (微米)
     */
    void setXYRadiusLimit(float limit) {
        xy_radius_limit = limit;
    }

    /**
     * @brief 获取Z轴限位
     * @return Z轴限位 (微米)
     */
    float getZRadiusLimit() const {
        return z_radius_limit;
    }

    /**
     * @brief 设置Z轴限位
     * @param limit Z轴限位 (微米)
     */
    void setZRadiusLimit(float limit) {
        z_radius_limit = limit;
    }

    // 步进电机参数访问接口
    /**
     * @brief 获取X轴单脉冲移动距离
     * @return X轴单脉冲移动距离 (微米)
     */
    float getXStepDist() const {
        return x_step_dist;
    }

    /**
     * @brief 设置X轴单脉冲移动距离
     * @param dist X轴单脉冲移动距离 (微米)
     */
    void setXStepDist(float dist) {
        x_step_dist = dist;
    }

    /**
     * @brief 获取Y轴单脉冲移动距离
     * @return Y轴单脉冲移动距离 (微米)
     */
    float getYStepDist() const {
        return y_step_dist;
    }

    /**
     * @brief 设置Y轴单脉冲移动距离
     * @param dist Y轴单脉冲移动距离 (微米)
     */
    void setYStepDist(float dist) {
        y_step_dist = dist;
    }

    /**
     * @brief 获取Z轴单脉冲移动距离
     * @return Z轴单脉冲移动距离 (微米)
     */
    float getZStepDist() const {
        return z_step_dist;
    }

    /**
     * @brief 设置Z轴单脉冲移动距离
     * @param dist Z轴单脉冲移动距离 (微米)
     */
    void setZStepDist(float dist) {
        z_step_dist = dist;
    }

    /**
     * @brief 获取配置摘要信息
     * @return 配置摘要字符串
     */
    QString getConfigSummary() const;

    /**
     * @brief 检查限位是否有效
     * @param x X坐标 (微米)
     * @param y Y坐标 (微米)
     * @param z Z坐标 (微米)
     * @return 如果在限位范围内返回true
     */
    bool isWithinLimits(float x, float y, float z) const;

    /**
     * @brief 将脉冲数转换为距离
     * @param pulses 脉冲数
     * @param axis 轴：0-X轴，1-Y轴，2-Z轴
     * @return 距离 (微米)
     */
    float pulsesToDistance(int pulses, int axis) const;

    /**
     * @brief 将距离转换为脉冲数
     * @param distance 距离 (微米)
     * @param axis 轴：0-X轴，1-Y轴，2-Z轴
     * @return 脉冲数
     */
    int distanceToPulses(float distance, int axis) const;

  private:
    // 限位参数
    float xy_radius_limit;  // XY轴限位半径 (微米)
    float z_radius_limit;   // Z轴限位 (微米)

    // 步进电机参数
    float x_step_dist;      // X轴单脉冲移动距离 (微米)
    float y_step_dist;      // Y轴单脉冲移动距离 (微米)
    float z_step_dist;      // Z轴单脉冲移动距离 (微米)

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;
};

}  // namespace Machine

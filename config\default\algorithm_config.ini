; Algorithm 配置文件
; 版本: 2.1.0
; 描述: 算法模块配置参数，包含传统算法、图像处理和光斑检测参数

[facula_detection]
; 光斑检测参数
facula_center_channels=       ; 多通道配置字符串 (QString)
facula_center_loc_x=160       ; 单点X坐标（兼容性） (uint8_t)
facula_center_loc_y=120       ; 单点Y坐标（兼容性） (uint8_t)
facula_center_peak_threshold=100  ; 多通道模式下的peak阈值 (uint32_t)
facula_handle_type=1          ; 处理类型：0-基础，1-增强，2-高精度 (uint8_t)

[image_processing]
; 图像处理参数
interpolation_type=1          ; 插值类型：0-最近邻，1-双线性，2-双三次，3-Lanczos (uint8_t)
interpolation_offset=0        ; 插值偏移量 (float)
filter_types=                 ; 滤波器类型列表，逗号分隔 (QString)
filter_strength=1             ; 全局滤波强度 (float)

; 卡尔曼滤波参数
kalman_strength=0.5           ; 卡尔曼滤波强度 (float)

; 高斯滤波参数
gaussian_sigma=1              ; 高斯滤波标准差 (float)
gaussian_kernel_size=3        ; 高斯滤波核大小 (uint8_t)
gaussian_preset=default       ; 高斯滤波预设 (QString)

; 双边滤波参数
bilateral_sigma_color=75      ; 双边滤波颜色标准差 (float)
bilateral_sigma_space=75      ; 双边滤波空间标准差 (float)
bilateral_kernel_size=5       ; 双边滤波核大小 (uint8_t)
bilateral_preset=default      ; 双边滤波预设 (QString)

; 加权均值滤波参数
weighted_avg_kernel_size=3    ; 加权均值滤波核大小 (uint8_t)
weighted_avg_preset=default   ; 加权均值滤波预设 (QString)

; 卷积滤波参数
convolution_kernel_size=3     ; 卷积核大小 (uint8_t)
convolution_preset=default    ; 卷积预设 (QString)

; 中值滤波参数
median_kernel_size=3          ; 中值滤波核大小 (uint8_t)
median_preset=default         ; 中值滤波预设 (QString)

[parameters]
; 检测参数
facula_threshold_min=50       ; 光斑检测最小阈值 (int)
facula_threshold_max=200      ; 光斑检测最大阈值 (int)
facula_area_min=10            ; 光斑最小面积 (int)
facula_area_max=1000          ; 光斑最大面积 (int)
facula_circularity_min=70     ; 光斑最小圆度 (float)
facula_circularity_max=100    ; 光斑最大圆度 (float)
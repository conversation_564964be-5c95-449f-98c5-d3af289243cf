#include "FaculaConfigData.h"
#include <QDebug>
#include <QRegularExpression>
#include <QStringList>

namespace PhotonSensor {

// 静态成员初始化
const QMap<QString, QString> FaculaConfigData::s_fieldTypes = {
    // 基本配置参数
    {"facula_center_channels", "QString"},
    {"facula_center_peak_threshold", "uint32_t"},
    {"facula_center_loc_x", "uint8_t"},
    {"facula_center_loc_y", "uint8_t"},
    {"facula_handle_type", "uint8_t"},

    // 运动控制参数
    {"initial_x_dist", "int32_t"},
    {"initial_y_dist", "int32_t"},
    {"initial_z_dist", "int32_t"},
    {"default_z_direct", "uint8_t"},
    {"z_move_step", "uint8_t"},

    // 搜索参数
    {"find_origin_raduis", "uint16_t"},
    {"find_angle_step", "uint8_t"},
    {"find_radius_step", "uint16_t"},
    {"find_times", "uint8_t"},
    {"peak_ok_threshold", "uint16_t"},

    // 基础判定参数
    {"peak_threshold", "uint16_t"},
    {"peak_max_threshold", "uint16_t"},
    {"edge_peak_threshold", "uint16_t"},

    // 对称性调节参数
    {"Amp_select", "uint8_t"},
    {"LR_peak_offset", "int8_t"},
    {"UD_peak_offset", "int8_t"},
    {"ALR_mp_peak", "uint8_t"},
    {"ALR_mp_peak_threshold", "uint8_t"},
    {"AUD_mp_peak", "uint8_t"},
    {"AUD_mp_peak_threshold", "uint8_t"},
    {"ACR_peak_delta", "uint8_t"},
    {"CR_peak_delta", "uint8_t"},
    {"ARR_peak_delta", "uint8_t"},
    {"Aedge_peak_threshold", "uint16_t"},
    {"AMax_peak", "uint16_t"},

    // 微调参数
    {"FT_adjust_peak_radio_threshold", "uint8_t"},
    {"FT_solid_peak_radio_threshold", "uint8_t"},
    {"FT_deflate_peak_radio_threshold", "uint8_t"},
    {"Uaround_peak_threshold", "uint8_t"},
    {"Apeak_offset_radio", "uint8_t"}};

const QMap<QString, QString> FaculaConfigData::s_fieldDescriptions = {
    // 基本配置参数
    {"facula_center_channels", "多通道配置字符串，格式：x1,y1;x2,y2;..."},
    {"facula_center_peak_threshold", "多通道模式下的peak阈值，范围100-2000"},
    {"facula_center_loc_x", "单点X坐标，兼容性参数，范围0-255"},
    {"facula_center_loc_y", "单点Y坐标，兼容性参数，范围0-255"},
    {"facula_handle_type", "光斑处理类型：0-基础，1-增强，2-高精度"},

    // 运动控制参数
    {"initial_x_dist", "X轴初始距离，单位：微米，范围-1000到1000"},
    {"initial_y_dist", "Y轴初始距离，单位：微米，范围-1000到1000"},
    {"initial_z_dist", "Z轴初始距离，单位：微米，范围-1000到1000"},
    {"default_z_direct", "Z轴默认方向：0-向下，1-向上"},
    {"z_move_step", "Z轴移动步长，单位：微米，范围1-10"},

    // 搜索参数
    {"find_origin_raduis", "搜索原点半径，单位：微米，范围50-300"},
    {"find_angle_step", "角度搜索步长，单位：度，范围5-45"},
    {"find_radius_step", "半径搜索步长，单位：微米，范围10-200"},
    {"find_times", "搜索次数，范围1-10"},
    {"peak_ok_threshold", "峰值OK阈值，范围100-1000"},

    // 基础判定参数
    {"peak_threshold", "峰值阈值，范围100-1000"},
    {"peak_max_threshold", "峰值最大阈值，范围1000-8000"},
    {"edge_peak_threshold", "边缘峰值阈值，范围50-500"},

    // 对称性调节参数
    {"Amp_select", "幅度选择参数，范围10-100"},
    {"LR_peak_offset", "左右峰值偏移，范围-50到50"},
    {"UD_peak_offset", "上下峰值偏移，范围-50到50"},
    {"ALR_mp_peak", "调节左右中点峰值，范围10-100"},
    {"ALR_mp_peak_threshold", "调节左右中点峰值阈值，范围50-200"},
    {"AUD_mp_peak", "调节上下中点峰值，范围0-100"},
    {"AUD_mp_peak_threshold", "调节上下中点峰值阈值，范围50-200"},
    {"ACR_peak_delta", "调节中心半径峰值差，范围50-200"},
    {"CR_peak_delta", "中心半径峰值差，范围20-100"},
    {"ARR_peak_delta", "调节半径半径峰值差，范围20-100"},
    {"Aedge_peak_threshold", "调节边缘峰值阈值，范围100-300"},
    {"AMax_peak", "调节最大峰值，范围1000-5000"},

    // 微调参数
    {"FT_adjust_peak_radio_threshold", "微调调节峰值比例阈值，范围50-100"},
    {"FT_solid_peak_radio_threshold", "微调固化峰值比例阈值，范围50-100"},
    {"FT_deflate_peak_radio_threshold", "微调收缩峰值比例阈值，范围50-100"},
    {"Uaround_peak_threshold", "周围峰值阈值，范围50-200"},
    {"Apeak_offset_radio", "峰值偏移比例，范围20-80"}};

const QStringList FaculaConfigData::s_handleTypeDescriptions = {"基础检测算法", "增强型检测算法", "高精度检测算法"};

FaculaConfigData::FaculaConfigData()
    : facula_center_channels("2,2"),
      facula_center_peak_threshold(800),
      facula_center_loc_x(2),
      facula_center_loc_y(2),
      facula_handle_type(1),
      // 初始化新增的XML参数
      initial_x_dist(0),
      initial_y_dist(0),
      initial_z_dist(0),
      default_z_direct(1),
      z_move_step(3),
      find_origin_raduis(140),
      find_angle_step(20),
      find_radius_step(140),
      find_times(4),
      peak_ok_threshold(550),
      peak_threshold(200),
      peak_max_threshold(4000),
      edge_peak_threshold(180),
      Amp_select(30),
      LR_peak_offset(0),
      UD_peak_offset(0),
      ALR_mp_peak(20),
      ALR_mp_peak_threshold(100),
      AUD_mp_peak(0),
      AUD_mp_peak_threshold(100),
      ACR_peak_delta(120),
      CR_peak_delta(50),
      ARR_peak_delta(40),
      Aedge_peak_threshold(180),
      AMax_peak(3000),
      FT_adjust_peak_radio_threshold(80),
      FT_solid_peak_radio_threshold(85),
      FT_deflate_peak_radio_threshold(75),
      Uaround_peak_threshold(100),
      Apeak_offset_radio(50) {
    setDefaults();
}

QVariantMap FaculaConfigData::toVariantMap() const {
    QVariantMap map;

    // 基本配置
    map["facula_center_channels"]       = facula_center_channels;
    map["facula_center_peak_threshold"] = facula_center_peak_threshold;
    map["facula_center_loc_x"]          = facula_center_loc_x;
    map["facula_center_loc_y"]          = facula_center_loc_y;
    map["facula_handle_type"]           = facula_handle_type;

    // Motion control parameters
    map["initial_x_dist"]   = initial_x_dist;
    map["initial_y_dist"]   = initial_y_dist;
    map["initial_z_dist"]   = initial_z_dist;
    map["default_z_direct"] = default_z_direct;
    map["z_move_step"]      = z_move_step;

    // Search parameters
    map["find_origin_raduis"] = find_origin_raduis;
    map["find_angle_step"]    = find_angle_step;
    map["find_radius_step"]   = find_radius_step;
    map["find_times"]         = find_times;
    map["discard_pack_num"]   = discard_pack_num;

    // Threshold parameters
    map["peak_ok_threshold"]   = peak_ok_threshold;
    map["peak_threshold"]      = peak_threshold;
    map["peak_max_threshold"]  = peak_max_threshold;
    map["edge_peak_threshold"] = edge_peak_threshold;

    // Symmetry parameters
    map["Amp_select"]            = Amp_select;
    map["LR_peak_offset"]        = LR_peak_offset;
    map["UD_peak_offset"]        = UD_peak_offset;
    map["ALR_mp_peak"]           = ALR_mp_peak;
    map["ALR_mp_peak_threshold"] = ALR_mp_peak_threshold;
    map["AUD_mp_peak"]           = AUD_mp_peak;
    map["AUD_mp_peak_threshold"] = AUD_mp_peak_threshold;
    map["ACR_peak_delta"]        = ACR_peak_delta;
    map["CR_peak_delta"]         = CR_peak_delta;
    map["ARR_peak_delta"]        = ARR_peak_delta;
    map["Aedge_peak_threshold"]  = Aedge_peak_threshold;
    map["AMax_peak"]             = AMax_peak;

    // Advanced parameters
    map["FT_adjust_peak_radio_threshold"]  = FT_adjust_peak_radio_threshold;
    map["FT_solid_peak_radio_threshold"]   = FT_solid_peak_radio_threshold;
    map["FT_deflate_peak_radio_threshold"] = FT_deflate_peak_radio_threshold;
    map["Uaround_peak_threshold"]          = Uaround_peak_threshold;
    map["Apeak_offset_radio"]              = Apeak_offset_radio;

    // FT微调参数
    map["FT_LRmp_adjust_peak"]            = FT_LRmp_adjust_peak;
    map["FT_LRmp_adjust_peak_threshold"]  = FT_LRmp_adjust_peak_threshold;
    map["FT_UDmp_adjust_peak"]            = FT_UDmp_adjust_peak;
    map["FT_UDmp_adjust_peak_threshold"]  = FT_UDmp_adjust_peak_threshold;
    map["FT_LRmp_solid_peak"]             = FT_LRmp_solid_peak;
    map["FT_LRmp_solid_peak_threshold"]   = FT_LRmp_solid_peak_threshold;
    map["FT_UDmp_solid_peak"]             = FT_UDmp_solid_peak;
    map["FT_UDmp_solid_peak_threshold"]   = FT_UDmp_solid_peak_threshold;
    map["FT_LRmp_deflate_peak"]           = FT_LRmp_deflate_peak;
    map["FT_LRmp_deflate_peak_threshold"] = FT_LRmp_deflate_peak_threshold;
    map["FT_UDmp_deflate_peak"]           = FT_UDmp_deflate_peak;
    map["FT_UDmp_deflate_peak_threshold"] = FT_UDmp_deflate_peak_threshold;

    // 添加元信息
    map["_type"]    = getTypeName();
    map["_version"] = getVersion();

    return map;
}

bool FaculaConfigData::fromVariantMap(const QVariantMap &data) {
    try {
        // 加载基本配置
        if (data.contains("facula_center_channels")) {
            setFaculaCenterChannels(data["facula_center_channels"].toString());
        }

        if (data.contains("facula_center_peak_threshold")) {
            facula_center_peak_threshold = data["facula_center_peak_threshold"].toUInt();
        }

        if (data.contains("facula_center_loc_x")) {
            facula_center_loc_x = data["facula_center_loc_x"].toUInt();
        }

        if (data.contains("facula_center_loc_y")) {
            facula_center_loc_y = data["facula_center_loc_y"].toUInt();
        }

        if (data.contains("facula_handle_type")) {
            facula_handle_type = data["facula_handle_type"].toUInt();
        }

        // 加载Motion control parameters
        if (data.contains("initial_x_dist")) {
            initial_x_dist = data["initial_x_dist"].toInt();
        }
        if (data.contains("initial_y_dist")) {
            initial_y_dist = data["initial_y_dist"].toInt();
        }
        if (data.contains("initial_z_dist")) {
            initial_z_dist = data["initial_z_dist"].toInt();
        }
        if (data.contains("default_z_direct")) {
            default_z_direct = data["default_z_direct"].toInt();
        }
        if (data.contains("z_move_step")) {
            z_move_step = data["z_move_step"].toInt();
        }

        // 加载Search parameters
        if (data.contains("find_origin_raduis")) {
            find_origin_raduis = data["find_origin_raduis"].toInt();
        }
        if (data.contains("find_angle_step")) {
            find_angle_step = data["find_angle_step"].toInt();
        }
        if (data.contains("find_radius_step")) {
            find_radius_step = data["find_radius_step"].toInt();
        }
        if (data.contains("find_times")) {
            find_times = data["find_times"].toInt();
        }
        if (data.contains("discard_pack_num")) {
            discard_pack_num = data["discard_pack_num"].toInt();
        }

        // 加载Threshold parameters
        if (data.contains("peak_ok_threshold")) {
            peak_ok_threshold = data["peak_ok_threshold"].toInt();
        }
        if (data.contains("peak_threshold")) {
            peak_threshold = data["peak_threshold"].toInt();
        }
        if (data.contains("peak_max_threshold")) {
            peak_max_threshold = data["peak_max_threshold"].toInt();
        }
        if (data.contains("edge_peak_threshold")) {
            edge_peak_threshold = data["edge_peak_threshold"].toInt();
        }

        // 加载Symmetry parameters
        if (data.contains("Amp_select")) {
            Amp_select = data["Amp_select"].toInt();
        }
        if (data.contains("LR_peak_offset")) {
            LR_peak_offset = data["LR_peak_offset"].toInt();
        }
        if (data.contains("UD_peak_offset")) {
            UD_peak_offset = data["UD_peak_offset"].toInt();
        }
        if (data.contains("ALR_mp_peak")) {
            ALR_mp_peak = data["ALR_mp_peak"].toInt();
        }
        if (data.contains("ALR_mp_peak_threshold")) {
            ALR_mp_peak_threshold = data["ALR_mp_peak_threshold"].toInt();
        }
        if (data.contains("AUD_mp_peak")) {
            AUD_mp_peak = data["AUD_mp_peak"].toInt();
        }
        if (data.contains("AUD_mp_peak_threshold")) {
            AUD_mp_peak_threshold = data["AUD_mp_peak_threshold"].toInt();
        }
        if (data.contains("ACR_peak_delta")) {
            ACR_peak_delta = data["ACR_peak_delta"].toInt();
        }
        if (data.contains("CR_peak_delta")) {
            CR_peak_delta = data["CR_peak_delta"].toInt();
        }
        if (data.contains("ARR_peak_delta")) {
            ARR_peak_delta = data["ARR_peak_delta"].toInt();
        }
        if (data.contains("Aedge_peak_threshold")) {
            Aedge_peak_threshold = data["Aedge_peak_threshold"].toInt();
        }
        if (data.contains("AMax_peak")) {
            AMax_peak = data["AMax_peak"].toInt();
        }

        // 加载Advanced parameters
        if (data.contains("FT_adjust_peak_radio_threshold")) {
            FT_adjust_peak_radio_threshold = data["FT_adjust_peak_radio_threshold"].toInt();
        }
        if (data.contains("FT_solid_peak_radio_threshold")) {
            FT_solid_peak_radio_threshold = data["FT_solid_peak_radio_threshold"].toInt();
        }
        if (data.contains("FT_deflate_peak_radio_threshold")) {
            FT_deflate_peak_radio_threshold = data["FT_deflate_peak_radio_threshold"].toInt();
        }
        if (data.contains("Uaround_peak_threshold")) {
            Uaround_peak_threshold = data["Uaround_peak_threshold"].toInt();
        }
        if (data.contains("Apeak_offset_radio")) {
            Apeak_offset_radio = data["Apeak_offset_radio"].toInt();
        }

        // 加载FT微调参数
        if (data.contains("FT_LRmp_adjust_peak")) {
            FT_LRmp_adjust_peak = data["FT_LRmp_adjust_peak"].toInt();
        }
        if (data.contains("FT_LRmp_adjust_peak_threshold")) {
            FT_LRmp_adjust_peak_threshold = data["FT_LRmp_adjust_peak_threshold"].toInt();
        }
        if (data.contains("FT_UDmp_adjust_peak")) {
            FT_UDmp_adjust_peak = data["FT_UDmp_adjust_peak"].toInt();
        }
        if (data.contains("FT_UDmp_adjust_peak_threshold")) {
            FT_UDmp_adjust_peak_threshold = data["FT_UDmp_adjust_peak_threshold"].toInt();
        }
        if (data.contains("FT_LRmp_solid_peak")) {
            FT_LRmp_solid_peak = data["FT_LRmp_solid_peak"].toInt();
        }
        if (data.contains("FT_LRmp_solid_peak_threshold")) {
            FT_LRmp_solid_peak_threshold = data["FT_LRmp_solid_peak_threshold"].toInt();
        }
        if (data.contains("FT_UDmp_solid_peak")) {
            FT_UDmp_solid_peak = data["FT_UDmp_solid_peak"].toInt();
        }
        if (data.contains("FT_UDmp_solid_peak_threshold")) {
            FT_UDmp_solid_peak_threshold = data["FT_UDmp_solid_peak_threshold"].toInt();
        }
        if (data.contains("FT_LRmp_deflate_peak")) {
            FT_LRmp_deflate_peak = data["FT_LRmp_deflate_peak"].toInt();
        }
        if (data.contains("FT_LRmp_deflate_peak_threshold")) {
            FT_LRmp_deflate_peak_threshold = data["FT_LRmp_deflate_peak_threshold"].toInt();
        }
        if (data.contains("FT_UDmp_deflate_peak")) {
            FT_UDmp_deflate_peak = data["FT_UDmp_deflate_peak"].toInt();
        }
        if (data.contains("FT_UDmp_deflate_peak_threshold")) {
            FT_UDmp_deflate_peak_threshold = data["FT_UDmp_deflate_peak_threshold"].toInt();
        }

        return true;
    } catch (const std::exception &e) {
        logError(QString("Failed to load from variant map: %1").arg(e.what()));
        return false;
    } catch (...) {
        logError("Unknown error loading from variant map");
        return false;
    }
}

bool FaculaConfigData::validate() const {
    // 验证通道配置字符串
    if (!validateChannelString(facula_center_channels)) {
        return false;
    }

    // 验证peak阈值
    if (!validatePeakThreshold(facula_center_peak_threshold)) {
        return false;
    }

    // 验证处理类型
    if (!validateHandleType(facula_handle_type)) {
        return false;
    }

    // 验证兼容性坐标
    if (facula_center_loc_x > MAX_COORDINATE || facula_center_loc_y > MAX_COORDINATE) {
        logError(QString("Invalid compatibility coordinates: (%1, %2)").arg(facula_center_loc_x).arg(facula_center_loc_y));
        return false;
    }

    return true;
}

void FaculaConfigData::setDefaults() {
    facula_center_channels       = "2,2";
    facula_center_peak_threshold = 800;
    facula_center_loc_x          = 2;
    facula_center_loc_y          = 2;
    facula_handle_type           = 1;

    // 设置Motion control parameters默认值
    initial_x_dist   = 0;
    initial_y_dist   = 0;
    initial_z_dist   = 0;
    default_z_direct = 1;
    z_move_step      = 3;

    // 设置Search parameters默认值
    find_origin_raduis = 140;
    find_angle_step    = 20;
    find_radius_step   = 140;
    find_times         = 4;
    discard_pack_num   = 1;  // 与旧XML系统保持一致

    // 设置Threshold parameters默认值（与旧XML系统保持一致）
    peak_ok_threshold   = 550;
    peak_threshold      = 600;   // 修正：旧系统使用600
    peak_max_threshold  = 1000;  // 修正：旧系统使用1000
    edge_peak_threshold = 50;    // 修正：旧系统使用50

    // 设置Symmetry parameters默认值
    Amp_select            = 30;
    LR_peak_offset        = 0;
    UD_peak_offset        = 0;
    ALR_mp_peak           = 20;
    ALR_mp_peak_threshold = 100;
    AUD_mp_peak           = 0;
    AUD_mp_peak_threshold = 100;
    ACR_peak_delta        = 120;
    CR_peak_delta         = 150;  // 修正：旧系统使用150
    ARR_peak_delta        = 40;
    Aedge_peak_threshold  = 180;
    AMax_peak             = 650;  // 修正：旧系统使用650

    // 设置Advanced parameters默认值
    FT_adjust_peak_radio_threshold  = 80;
    FT_solid_peak_radio_threshold   = 85;
    FT_deflate_peak_radio_threshold = 75;
    Uaround_peak_threshold          = 60;  // 修正：旧系统使用60
    Apeak_offset_radio              = 50;

    // 设置FT微调参数默认值（与旧XML系统保持一致）
    FT_LRmp_adjust_peak            = 25;
    FT_LRmp_adjust_peak_threshold  = 5;
    FT_UDmp_adjust_peak            = 0;
    FT_UDmp_adjust_peak_threshold  = 100;
    FT_LRmp_solid_peak             = 25;
    FT_LRmp_solid_peak_threshold   = 8;
    FT_UDmp_solid_peak             = 0;
    FT_UDmp_solid_peak_threshold   = 100;
    FT_LRmp_deflate_peak           = 25;
    FT_LRmp_deflate_peak_threshold = 8;
    FT_UDmp_deflate_peak           = 0;
    FT_UDmp_deflate_peak_threshold = 100;

    // 初始化默认通道点
    facula_center_points.clear();
    facula_center_points.append(QPoint(2, 2));

    logInfo("Set facula config to default values with all XML parameters");
}

QStringList FaculaConfigData::getFieldNames() const {
    return s_fieldTypes.keys();
}

QString FaculaConfigData::getFieldType(const QString &fieldName) const {
    return s_fieldTypes.value(fieldName, "unknown");
}

QString FaculaConfigData::getFieldDescription(const QString &fieldName) const {
    return s_fieldDescriptions.value(fieldName, "No description available");
}

bool FaculaConfigData::hasField(const QString &fieldName) const {
    return s_fieldTypes.contains(fieldName);
}

QVariant FaculaConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    if (fieldName == "facula_center_channels") {
        return facula_center_channels;
    } else if (fieldName == "facula_center_peak_threshold") {
        return facula_center_peak_threshold;
    } else if (fieldName == "facula_center_loc_x") {
        return facula_center_loc_x;
    } else if (fieldName == "facula_center_loc_y") {
        return facula_center_loc_y;
    } else if (fieldName == "facula_handle_type") {
        return facula_handle_type;
    }

    return defaultValue;
}

bool FaculaConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    if (fieldName == "facula_center_channels") {
        setFaculaCenterChannels(value.toString());
        return true;
    } else if (fieldName == "facula_center_peak_threshold") {
        facula_center_peak_threshold = value.toUInt();
        return true;
    } else if (fieldName == "facula_center_loc_x") {
        facula_center_loc_x = value.toUInt();
        return true;
    } else if (fieldName == "facula_center_loc_y") {
        facula_center_loc_y = value.toUInt();
        return true;
    } else if (fieldName == "facula_handle_type") {
        facula_handle_type = value.toUInt();
        return true;
    }

    return false;
}

bool FaculaConfigData::resetField(const QString &fieldName) {
    if (fieldName == "facula_center_channels") {
        facula_center_channels = "2,2";
        syncChannelData();
        return true;
    } else if (fieldName == "facula_center_peak_threshold") {
        facula_center_peak_threshold = 800;
        return true;
    } else if (fieldName == "facula_center_loc_x") {
        facula_center_loc_x = 2;
        return true;
    } else if (fieldName == "facula_center_loc_y") {
        facula_center_loc_y = 2;
        return true;
    } else if (fieldName == "facula_handle_type") {
        facula_handle_type = 1;
        return true;
    }

    return false;
}

void FaculaConfigData::setFaculaCenterChannels(const QString &channels) {
    facula_center_channels = channels;
    syncChannelData();
}

void FaculaConfigData::setFaculaCenterPoints(const QVector<QPoint> &points) {
    facula_center_points   = points;
    facula_center_channels = pointsToChannelString(points);
}

QString FaculaConfigData::getFaculaHandleTypeDescription() const {
    if (facula_handle_type < s_handleTypeDescriptions.size()) {
        return s_handleTypeDescriptions[facula_handle_type];
    }
    return "未知处理类型";
}

QVector<QPoint> FaculaConfigData::parseChannelString(const QString &channels) {
    QVector<QPoint> points;

    if (channels.isEmpty()) {
        return points;
    }

    // 支持两种格式：
    // 1. 简单格式："2,2" -> QPoint(2, 2)
    // 2. 多通道格式："2,2;3,3;4,4" -> [QPoint(2,2), QPoint(3,3), QPoint(4,4)]

    QStringList channelList = channels.split(';', Qt::SkipEmptyParts);

    for (const QString &channel : channelList) {
        QStringList coords = channel.split(',', Qt::SkipEmptyParts);
        if (coords.size() == 2) {
            bool xOk, yOk;
            int  x = coords[0].trimmed().toInt(&xOk);
            int  y = coords[1].trimmed().toInt(&yOk);

            if (xOk && yOk && x >= 0 && x <= 255 && y >= 0 && y <= 255) {
                points.append(QPoint(x, y));
            }
        }
    }

    return points;
}

QString FaculaConfigData::pointsToChannelString(const QVector<QPoint> &points) {
    QStringList channelList;

    for (const QPoint &point : points) {
        channelList.append(QString("%1,%2").arg(point.x()).arg(point.y()));
    }

    return channelList.join(';');
}

void FaculaConfigData::addChannelPoint(const QPoint &point) {
    facula_center_points.append(point);
    facula_center_channels = pointsToChannelString(facula_center_points);
}

bool FaculaConfigData::removeChannelPoint(int index) {
    if (index >= 0 && index < facula_center_points.size()) {
        facula_center_points.removeAt(index);
        facula_center_channels = pointsToChannelString(facula_center_points);
        return true;
    }
    return false;
}

void FaculaConfigData::clearChannelPoints() {
    facula_center_points.clear();
    facula_center_channels.clear();
}

bool FaculaConfigData::isDefaultConfig() const {
    return facula_center_channels == "2,2" && facula_center_peak_threshold == 800 && facula_center_loc_x == 2 && facula_center_loc_y == 2 &&
           facula_handle_type == 1;
}

QString FaculaConfigData::getConfigSummary() const {
    return QString("光斑配置: 通道=%1, 阈值=%2, 类型=%3").arg(facula_center_channels).arg(facula_center_peak_threshold).arg(getFaculaHandleTypeDescription());
}

bool FaculaConfigData::validateChannelString(const QString &channels) const {
    if (channels.isEmpty()) {
        logError("Channel string cannot be empty");
        return false;
    }

    QVector<QPoint> points = parseChannelString(channels);
    if (points.isEmpty()) {
        logError(QString("Invalid channel string format: %1").arg(channels));
        return false;
    }

    return true;
}

bool FaculaConfigData::validatePeakThreshold(uint32_t threshold) const {
    if (threshold < MIN_PEAK_THRESHOLD || threshold > MAX_PEAK_THRESHOLD) {
        logError(QString("Peak threshold %1 out of range [%2, %3]").arg(threshold).arg(MIN_PEAK_THRESHOLD).arg(MAX_PEAK_THRESHOLD));
        return false;
    }
    return true;
}

bool FaculaConfigData::validateHandleType(uint8_t type) const {
    if (type > MAX_HANDLE_TYPE) {
        logError(QString("Handle type %1 out of range [0, %2]").arg(type).arg(MAX_HANDLE_TYPE));
        return false;
    }
    return true;
}

void FaculaConfigData::syncChannelData() {
    facula_center_points = parseChannelString(facula_center_channels);
}

}  // namespace PhotonSensor

// 自动注册光斑配置类型
REGISTER_CONFIG_TYPE(PhotonSensor::FaculaConfigData, "Facula", "1.0.0", "光斑检测配置，包含多通道光斑中心和处理参数")

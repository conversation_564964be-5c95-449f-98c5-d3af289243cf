#include "AlgorithmConfigProvider.h"
#include <QApplication>
#include <QDebug>

namespace Algorithm {

// 静态常量定义 - 参数键名
const QString AlgorithmConfigProvider::KEY_INITIAL_X_DIST     = "initial_x_dist";
const QString AlgorithmConfigProvider::KEY_INITIAL_Y_DIST     = "initial_y_dist";
const QString AlgorithmConfigProvider::KEY_INITIAL_Z_DIST     = "initial_z_dist";
const QString AlgorithmConfigProvider::KEY_FIND_ORIGIN_RADIUS = "find_origin_radius";
const QString AlgorithmConfigProvider::KEY_FIND_TIMES         = "find_times";
const QString AlgorithmConfigProvider::KEY_PEAK_OK_THRESHOLD  = "peak_ok_threshold";
const QString AlgorithmConfigProvider::KEY_Z_MOVE_STEP        = "z_move_step";
const QString AlgorithmConfigProvider::KEY_XY_MOVE_STEP       = "xy_move_step";
const QString AlgorithmConfigProvider::KEY_MAX_ADJUST_TIMES   = "max_adjust_times";
const QString AlgorithmConfigProvider::KEY_ADJUST_PRECISION   = "adjust_precision";
const QString AlgorithmConfigProvider::KEY_INTERPOLATION_TYPE = "interpolation_type";
const QString AlgorithmConfigProvider::KEY_FILTER_TYPES       = "filter_types";
const QString AlgorithmConfigProvider::KEY_FILTER_STRENGTH    = "filter_strength";

// 静态常量定义 - 默认值
const int AlgorithmConfigProvider::DEFAULT_INITIAL_X_DIST     = 0;
const int AlgorithmConfigProvider::DEFAULT_INITIAL_Y_DIST     = 0;
const int AlgorithmConfigProvider::DEFAULT_INITIAL_Z_DIST     = 0;
const int AlgorithmConfigProvider::DEFAULT_FIND_ORIGIN_RADIUS = 140;
const int AlgorithmConfigProvider::DEFAULT_FIND_TIMES         = 4;
const int AlgorithmConfigProvider::DEFAULT_PEAK_OK_THRESHOLD  = 550;
const int AlgorithmConfigProvider::DEFAULT_Z_MOVE_STEP        = 3;
const int AlgorithmConfigProvider::DEFAULT_XY_MOVE_STEP       = 10;
const int AlgorithmConfigProvider::DEFAULT_MAX_ADJUST_TIMES   = 10;
const int AlgorithmConfigProvider::DEFAULT_ADJUST_PRECISION   = 1;
const int AlgorithmConfigProvider::DEFAULT_INTERPOLATION_TYPE = 0;
const int AlgorithmConfigProvider::DEFAULT_FILTER_TYPES       = 6;
const int AlgorithmConfigProvider::DEFAULT_FILTER_STRENGTH    = 1;

// 静态常量定义 - 验证范围
const int AlgorithmConfigProvider::MIN_DISTANCE           = -1000;
const int AlgorithmConfigProvider::MAX_DISTANCE           = 1000;
const int AlgorithmConfigProvider::MIN_RADIUS             = 10;
const int AlgorithmConfigProvider::MAX_RADIUS             = 500;
const int AlgorithmConfigProvider::MIN_TIMES              = 1;
const int AlgorithmConfigProvider::MAX_TIMES              = 20;
const int AlgorithmConfigProvider::MIN_THRESHOLD          = 100;
const int AlgorithmConfigProvider::MAX_THRESHOLD          = 2000;
const int AlgorithmConfigProvider::MIN_STEP               = 1;
const int AlgorithmConfigProvider::MAX_STEP               = 100;
const int AlgorithmConfigProvider::MIN_PRECISION          = 1;
const int AlgorithmConfigProvider::MAX_PRECISION          = 10;
const int AlgorithmConfigProvider::MAX_INTERPOLATION_TYPE = 3;
const int AlgorithmConfigProvider::MAX_FILTER_TYPES       = 10;
const int AlgorithmConfigProvider::MAX_FILTER_STRENGTH    = 10;

AlgorithmConfigProvider::AlgorithmConfigProvider(QObject *parent) : BaseConfigProvider(parent) {
    // 在子类构造函数中调用加载默认参数
    loadDefaultParameters();
    logInfo("AlgorithmConfigProvider initialized");
}

QString AlgorithmConfigProvider::getConfigFilePath() const {
    return QApplication::applicationDirPath() + "/config/modules/algorithm/algorithm_config.ini";
}

void AlgorithmConfigProvider::loadDefaultParameters() {
    // 设置默认参数值
    setParameterDefault(KEY_INITIAL_X_DIST, DEFAULT_INITIAL_X_DIST);
    setParameterDefault(KEY_INITIAL_Y_DIST, DEFAULT_INITIAL_Y_DIST);
    setParameterDefault(KEY_INITIAL_Z_DIST, DEFAULT_INITIAL_Z_DIST);
    setParameterDefault(KEY_FIND_ORIGIN_RADIUS, DEFAULT_FIND_ORIGIN_RADIUS);
    setParameterDefault(KEY_FIND_TIMES, DEFAULT_FIND_TIMES);
    setParameterDefault(KEY_PEAK_OK_THRESHOLD, DEFAULT_PEAK_OK_THRESHOLD);
    setParameterDefault(KEY_Z_MOVE_STEP, DEFAULT_Z_MOVE_STEP);
    setParameterDefault(KEY_XY_MOVE_STEP, DEFAULT_XY_MOVE_STEP);
    setParameterDefault(KEY_MAX_ADJUST_TIMES, DEFAULT_MAX_ADJUST_TIMES);
    setParameterDefault(KEY_ADJUST_PRECISION, DEFAULT_ADJUST_PRECISION);
    setParameterDefault(KEY_INTERPOLATION_TYPE, DEFAULT_INTERPOLATION_TYPE);
    setParameterDefault(KEY_FILTER_TYPES, DEFAULT_FILTER_TYPES);
    setParameterDefault(KEY_FILTER_STRENGTH, DEFAULT_FILTER_STRENGTH);

    // 设置参数信息
    setParameterInfo(KEY_INITIAL_X_DIST, "初始X距离", "int", QString("%1 to %2").arg(MIN_DISTANCE).arg(MAX_DISTANCE));
    setParameterInfo(KEY_INITIAL_Y_DIST, "初始Y距离", "int", QString("%1 to %2").arg(MIN_DISTANCE).arg(MAX_DISTANCE));
    setParameterInfo(KEY_INITIAL_Z_DIST, "初始Z距离", "int", QString("%1 to %2").arg(MIN_DISTANCE).arg(MAX_DISTANCE));
    setParameterInfo(KEY_FIND_ORIGIN_RADIUS, "寻找原点半径", "int", QString("%1 to %2").arg(MIN_RADIUS).arg(MAX_RADIUS));
    setParameterInfo(KEY_FIND_TIMES, "寻找次数", "int", QString("%1 to %2").arg(MIN_TIMES).arg(MAX_TIMES));
    setParameterInfo(KEY_PEAK_OK_THRESHOLD, "峰值OK阈值", "int", QString("%1 to %2").arg(MIN_THRESHOLD).arg(MAX_THRESHOLD));
    setParameterInfo(KEY_Z_MOVE_STEP, "Z轴移动步长", "int", QString("%1 to %2").arg(MIN_STEP).arg(MAX_STEP));
    setParameterInfo(KEY_XY_MOVE_STEP, "XY轴移动步长", "int", QString("%1 to %2").arg(MIN_STEP).arg(MAX_STEP));
    setParameterInfo(KEY_MAX_ADJUST_TIMES, "最大调节次数", "int", QString("%1 to %2").arg(MIN_TIMES).arg(MAX_TIMES));
    setParameterInfo(KEY_ADJUST_PRECISION, "调节精度", "int", QString("%1 to %2").arg(MIN_PRECISION).arg(MAX_PRECISION));
    setParameterInfo(KEY_INTERPOLATION_TYPE, "插值类型", "int", QString("0 to %1").arg(MAX_INTERPOLATION_TYPE));
    setParameterInfo(KEY_FILTER_TYPES, "滤波器类型", "int", QString("0 to %1").arg(MAX_FILTER_TYPES));
    setParameterInfo(KEY_FILTER_STRENGTH, "滤波器强度", "int", QString("0 to %1").arg(MAX_FILTER_STRENGTH));

    logInfo("Default algorithm parameters loaded");
}

Config::ConfigResult AlgorithmConfigProvider::validateParameter(const QString &key, const QVariant &value) const {
    // 先调用基类验证
    Config::ConfigResult baseResult = BaseConfigProvider::validateParameter(key, value);
    if (!baseResult.success) {
        return baseResult;
    }

    int intValue = value.toInt();

    // 算法特定的验证
    if (key == KEY_INITIAL_X_DIST || key == KEY_INITIAL_Y_DIST || key == KEY_INITIAL_Z_DIST) {
        if (!isValidDistance(intValue)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Distance %1 out of range [%2-%3]").arg(intValue).arg(MIN_DISTANCE).arg(MAX_DISTANCE));
        }
    } else if (key == KEY_FIND_ORIGIN_RADIUS) {
        if (!isValidRadius(intValue)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Radius %1 out of range [%2-%3]").arg(intValue).arg(MIN_RADIUS).arg(MAX_RADIUS));
        }
    } else if (key == KEY_FIND_TIMES || key == KEY_MAX_ADJUST_TIMES) {
        if (!isValidTimes(intValue)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Times %1 out of range [%2-%3]").arg(intValue).arg(MIN_TIMES).arg(MAX_TIMES));
        }
    } else if (key == KEY_PEAK_OK_THRESHOLD) {
        if (!isValidThreshold(intValue)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Threshold %1 out of range [%2-%3]").arg(intValue).arg(MIN_THRESHOLD).arg(MAX_THRESHOLD));
        }
    } else if (key == KEY_Z_MOVE_STEP || key == KEY_XY_MOVE_STEP) {
        if (!isValidStep(intValue)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Step %1 out of range [%2-%3]").arg(intValue).arg(MIN_STEP).arg(MAX_STEP));
        }
    } else if (key == KEY_ADJUST_PRECISION) {
        if (!isValidPrecision(intValue)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Precision %1 out of range [%2-%3]").arg(intValue).arg(MIN_PRECISION).arg(MAX_PRECISION));
        }
    } else if (key == KEY_INTERPOLATION_TYPE) {
        if (!isValidInterpolationType(intValue)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Interpolation type %1 out of range [0-%2]").arg(intValue).arg(MAX_INTERPOLATION_TYPE));
        }
    } else if (key == KEY_FILTER_TYPES) {
        if (!isValidFilterTypes(intValue)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Filter types %1 out of range [0-%2]").arg(intValue).arg(MAX_FILTER_TYPES));
        }
    } else if (key == KEY_FILTER_STRENGTH) {
        if (!isValidFilterStrength(intValue)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Filter strength %1 out of range [0-%2]").arg(intValue).arg(MAX_FILTER_STRENGTH));
        }
    }

    return Config::ConfigResult(true);
}

QString AlgorithmConfigProvider::getParameterRange(const QString &key) const {
    if (key == KEY_INITIAL_X_DIST || key == KEY_INITIAL_Y_DIST || key == KEY_INITIAL_Z_DIST) {
        return QString("%1 to %2").arg(MIN_DISTANCE).arg(MAX_DISTANCE);
    } else if (key == KEY_FIND_ORIGIN_RADIUS) {
        return QString("%1 to %2").arg(MIN_RADIUS).arg(MAX_RADIUS);
    } else if (key == KEY_FIND_TIMES || key == KEY_MAX_ADJUST_TIMES) {
        return QString("%1 to %2").arg(MIN_TIMES).arg(MAX_TIMES);
    } else if (key == KEY_PEAK_OK_THRESHOLD) {
        return QString("%1 to %2").arg(MIN_THRESHOLD).arg(MAX_THRESHOLD);
    } else if (key == KEY_Z_MOVE_STEP || key == KEY_XY_MOVE_STEP) {
        return QString("%1 to %2").arg(MIN_STEP).arg(MAX_STEP);
    } else if (key == KEY_ADJUST_PRECISION) {
        return QString("%1 to %2").arg(MIN_PRECISION).arg(MAX_PRECISION);
    } else if (key == KEY_INTERPOLATION_TYPE) {
        return QString("0 to %1").arg(MAX_INTERPOLATION_TYPE);
    } else if (key == KEY_FILTER_TYPES) {
        return QString("0 to %1").arg(MAX_FILTER_TYPES);
    } else if (key == KEY_FILTER_STRENGTH) {
        return QString("0 to %1").arg(MAX_FILTER_STRENGTH);
    }

    return BaseConfigProvider::getParameterRange(key);
}

void AlgorithmConfigProvider::onParameterChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue) {
    BaseConfigProvider::onParameterChanged(key, oldValue, newValue);

    logInfo(QString("Algorithm parameter updated: %1 = %2 (was %3)").arg(key, newValue.toString(), oldValue.toString()));
}

// 参数访问方法实现
int AlgorithmConfigProvider::getInitialXDist() const {
    return getParameter(KEY_INITIAL_X_DIST, DEFAULT_INITIAL_X_DIST).toInt();
}

void AlgorithmConfigProvider::setInitialXDist(int dist) {
    setParameter(KEY_INITIAL_X_DIST, dist);
}

int AlgorithmConfigProvider::getInitialYDist() const {
    return getParameter(KEY_INITIAL_Y_DIST, DEFAULT_INITIAL_Y_DIST).toInt();
}

void AlgorithmConfigProvider::setInitialYDist(int dist) {
    setParameter(KEY_INITIAL_Y_DIST, dist);
}

int AlgorithmConfigProvider::getInitialZDist() const {
    return getParameter(KEY_INITIAL_Z_DIST, DEFAULT_INITIAL_Z_DIST).toInt();
}

void AlgorithmConfigProvider::setInitialZDist(int dist) {
    setParameter(KEY_INITIAL_Z_DIST, dist);
}

int AlgorithmConfigProvider::getFindOriginRadius() const {
    return getParameter(KEY_FIND_ORIGIN_RADIUS, DEFAULT_FIND_ORIGIN_RADIUS).toInt();
}

void AlgorithmConfigProvider::setFindOriginRadius(int radius) {
    setParameter(KEY_FIND_ORIGIN_RADIUS, radius);
}

int AlgorithmConfigProvider::getFindTimes() const {
    return getParameter(KEY_FIND_TIMES, DEFAULT_FIND_TIMES).toInt();
}

void AlgorithmConfigProvider::setFindTimes(int times) {
    setParameter(KEY_FIND_TIMES, times);
}

int AlgorithmConfigProvider::getPeakOkThreshold() const {
    return getParameter(KEY_PEAK_OK_THRESHOLD, DEFAULT_PEAK_OK_THRESHOLD).toInt();
}

void AlgorithmConfigProvider::setPeakOkThreshold(int threshold) {
    setParameter(KEY_PEAK_OK_THRESHOLD, threshold);
}

int AlgorithmConfigProvider::getZMoveStep() const {
    return getParameter(KEY_Z_MOVE_STEP, DEFAULT_Z_MOVE_STEP).toInt();
}

void AlgorithmConfigProvider::setZMoveStep(int step) {
    setParameter(KEY_Z_MOVE_STEP, step);
}

int AlgorithmConfigProvider::getXYMoveStep() const {
    return getParameter(KEY_XY_MOVE_STEP, DEFAULT_XY_MOVE_STEP).toInt();
}

void AlgorithmConfigProvider::setXYMoveStep(int step) {
    setParameter(KEY_XY_MOVE_STEP, step);
}

int AlgorithmConfigProvider::getMaxAdjustTimes() const {
    return getParameter(KEY_MAX_ADJUST_TIMES, DEFAULT_MAX_ADJUST_TIMES).toInt();
}

void AlgorithmConfigProvider::setMaxAdjustTimes(int times) {
    setParameter(KEY_MAX_ADJUST_TIMES, times);
}

int AlgorithmConfigProvider::getAdjustPrecision() const {
    return getParameter(KEY_ADJUST_PRECISION, DEFAULT_ADJUST_PRECISION).toInt();
}

void AlgorithmConfigProvider::setAdjustPrecision(int precision) {
    setParameter(KEY_ADJUST_PRECISION, precision);
}

int AlgorithmConfigProvider::getInterpolationType() const {
    return getParameter(KEY_INTERPOLATION_TYPE, DEFAULT_INTERPOLATION_TYPE).toInt();
}

void AlgorithmConfigProvider::setInterpolationType(int type) {
    setParameter(KEY_INTERPOLATION_TYPE, type);
}

int AlgorithmConfigProvider::getFilterTypes() const {
    return getParameter(KEY_FILTER_TYPES, DEFAULT_FILTER_TYPES).toInt();
}

void AlgorithmConfigProvider::setFilterTypes(int types) {
    setParameter(KEY_FILTER_TYPES, types);
}

int AlgorithmConfigProvider::getFilterStrength() const {
    return getParameter(KEY_FILTER_STRENGTH, DEFAULT_FILTER_STRENGTH).toInt();
}

void AlgorithmConfigProvider::setFilterStrength(int strength) {
    setParameter(KEY_FILTER_STRENGTH, strength);
}

// 验证方法实现
bool AlgorithmConfigProvider::isValidDistance(int value) const {
    return value >= MIN_DISTANCE && value <= MAX_DISTANCE;
}

bool AlgorithmConfigProvider::isValidRadius(int value) const {
    return value >= MIN_RADIUS && value <= MAX_RADIUS;
}

bool AlgorithmConfigProvider::isValidTimes(int value) const {
    return value >= MIN_TIMES && value <= MAX_TIMES;
}

bool AlgorithmConfigProvider::isValidThreshold(int value) const {
    return value >= MIN_THRESHOLD && value <= MAX_THRESHOLD;
}

bool AlgorithmConfigProvider::isValidStep(int value) const {
    return value >= MIN_STEP && value <= MAX_STEP;
}

bool AlgorithmConfigProvider::isValidPrecision(int value) const {
    return value >= MIN_PRECISION && value <= MAX_PRECISION;
}

bool AlgorithmConfigProvider::isValidInterpolationType(int value) const {
    return value >= 0 && value <= MAX_INTERPOLATION_TYPE;
}

bool AlgorithmConfigProvider::isValidFilterTypes(int value) const {
    return value >= 0 && value <= MAX_FILTER_TYPES;
}

bool AlgorithmConfigProvider::isValidFilterStrength(int value) const {
    return value >= 0 && value <= MAX_FILTER_STRENGTH;
}

}  // namespace Algorithm

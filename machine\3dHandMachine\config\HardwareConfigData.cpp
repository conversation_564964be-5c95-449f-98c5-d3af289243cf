#include "HardwareConfigData.h"
#include "../../../qtDebug/qLog.h"
#include <QDebug>
#include <cmath>

// 自动注册配置类型
REGISTER_CONFIG_TYPE(Machine::HardwareConfigData, "Hardware", "1.0.0", "硬件配置，包含步进电机和限位参数")

namespace Machine {

// 静态字段类型映射表
const QMap<QString, QString> HardwareConfigData::s_fieldTypes = {{"xy_radius_limit", "float"},
                                                                 {"z_radius_limit", "float"},
                                                                 {"x_step_dist", "float"},
                                                                 {"y_step_dist", "float"},
                                                                 {"z_step_dist", "float"}};

// 静态字段描述映射表
const QMap<QString, QString> HardwareConfigData::s_fieldDescriptions = {{"xy_radius_limit", "XY轴限位半径 (100-3000微米)"},
                                                                        {"z_radius_limit", "Z轴限位 (100-3000微米)"},
                                                                        {"x_step_dist", "X轴单脉冲移动距离 (0.1-10.0微米)"},
                                                                        {"y_step_dist", "Y轴单脉冲移动距离 (0.1-10.0微米)"},
                                                                        {"z_step_dist", "Z轴单脉冲移动距离 (0.1-10.0微米)"}};

HardwareConfigData::HardwareConfigData() {
    setDefaults();
}

void HardwareConfigData::setDefaults() {
    // 限位参数
    xy_radius_limit = 1000.0f;  // 默认XY轴限位半径1000微米
    z_radius_limit  = 500.0f;   // 默认Z轴限位500微米

    // 步进电机参数
    x_step_dist = 0.625f;  // 默认X轴单脉冲移动距离0.625微米
    y_step_dist = 0.625f;  // 默认Y轴单脉冲移动距离0.625微米
    z_step_dist = 0.625f;  // 默认Z轴单脉冲移动距离0.625微米

    qDebug() << "[Hardware] setDefaults called - XY:" << xy_radius_limit << "Z:" << z_radius_limit << "X_step:" << x_step_dist;
}

QVariantMap HardwareConfigData::toVariantMap() const {
    QVariantMap result;

    // 限位参数
    result["xy_radius_limit"] = xy_radius_limit;
    result["z_radius_limit"]  = z_radius_limit;

    // 步进电机参数
    result["x_step_dist"] = x_step_dist;
    result["y_step_dist"] = y_step_dist;
    result["z_step_dist"] = z_step_dist;

    return result;
}

bool HardwareConfigData::fromVariantMap(const QVariantMap &data) {
    bool success = true;

    // 限位参数
    if (data.contains("xy_radius_limit")) {
        xy_radius_limit = data["xy_radius_limit"].toFloat();
    }
    if (data.contains("z_radius_limit")) {
        z_radius_limit = data["z_radius_limit"].toFloat();
    }

    // 步进电机参数
    if (data.contains("x_step_dist")) {
        x_step_dist = data["x_step_dist"].toFloat();
    }
    if (data.contains("y_step_dist")) {
        y_step_dist = data["y_step_dist"].toFloat();
    }
    if (data.contains("z_step_dist")) {
        z_step_dist = data["z_step_dist"].toFloat();
    }

    return success && validate();
}

bool HardwareConfigData::validate() const {
    // 验证限位参数
    if (xy_radius_limit <= 0.0f) {
        qWarning() << "Invalid xy_radius_limit:" << xy_radius_limit;
        return false;
    }
    if (z_radius_limit <= 0.0f) {
        qWarning() << "Invalid z_radius_limit:" << z_radius_limit;
        return false;
    }

    // 验证步进电机参数
    if (x_step_dist <= 0.0f) {
        qWarning() << "Invalid x_step_dist:" << x_step_dist;
        return false;
    }
    if (y_step_dist <= 0.0f) {
        qWarning() << "Invalid y_step_dist:" << y_step_dist;
        return false;
    }
    if (z_step_dist <= 0.0f) {
        qWarning() << "Invalid z_step_dist:" << z_step_dist;
        return false;
    }

    return true;
}

QStringList HardwareConfigData::getFieldNames() const {
    return s_fieldTypes.keys();
}

QString HardwareConfigData::getFieldType(const QString &fieldName) const {
    return s_fieldTypes.value(fieldName, QString());
}

QString HardwareConfigData::getFieldDescription(const QString &fieldName) const {
    return s_fieldDescriptions.value(fieldName, QString());
}

bool HardwareConfigData::hasField(const QString &fieldName) const {
    return s_fieldTypes.contains(fieldName);
}

QVariant HardwareConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    QVariantMap data = toVariantMap();
    return data.value(fieldName, defaultValue);
}

bool HardwareConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    QVariantMap data;
    data[fieldName] = value;
    return fromVariantMap(data);
}

bool HardwareConfigData::resetField(const QString &fieldName) {
    if (!hasField(fieldName)) {
        return false;
    }

    // 重置为默认值
    HardwareConfigData defaultConfig;
    QVariantMap        defaultData = defaultConfig.toVariantMap();

    if (defaultData.contains(fieldName)) {
        return setFieldValue(fieldName, defaultData[fieldName]);
    }

    return false;
}

QString HardwareConfigData::getConfigSummary() const {
    return QString("XY限位: %1μm, Z限位: %2μm, 步距: X=%3μm, Y=%4μm, Z=%5μm")
        .arg(xy_radius_limit)
        .arg(z_radius_limit)
        .arg(x_step_dist)
        .arg(y_step_dist)
        .arg(z_step_dist);
}

bool HardwareConfigData::isWithinLimits(float x, float y, float z) const {
    // 检查XY平面限位
    float xy_distance = std::sqrt(x * x + y * y);
    if (xy_distance > xy_radius_limit) {
        return false;
    }

    // 检查Z轴限位
    if (std::abs(z) > z_radius_limit) {
        return false;
    }

    return true;
}

float HardwareConfigData::pulsesToDistance(int pulses, int axis) const {
    switch (axis) {
    case 0:  // X轴
        return pulses * x_step_dist;
    case 1:  // Y轴
        return pulses * y_step_dist;
    case 2:  // Z轴
        return pulses * z_step_dist;
    default:
        qWarning() << "Invalid axis:" << axis;
        return 0.0f;
    }
}

int HardwareConfigData::distanceToPulses(float distance, int axis) const {
    switch (axis) {
    case 0:                                                      // X轴
        return static_cast<int>(distance / x_step_dist + 0.5f);  // 四舍五入
    case 1:                                                      // Y轴
        return static_cast<int>(distance / y_step_dist + 0.5f);  // 四舍五入
    case 2:                                                      // Z轴
        return static_cast<int>(distance / z_step_dist + 0.5f);  // 四舍五入
    default:
        qWarning() << "Invalid axis:" << axis;
        return 0;
    }
}

}  // namespace Machine

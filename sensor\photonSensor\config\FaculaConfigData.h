#pragma once

#include "../../../components/configModule/ConfigTypeRegistry.h"
#include "../../../components/configModule/IConfigData.h"
#include <QPoint>
#include <QString>
#include <QStringList>
#include <QVariantMap>
#include <QVector>

namespace PhotonSensor {

/**
 * @brief 光斑配置数据
 *
 * 管理光斑检测和处理的所有配置参数：
 * - 多通道光斑中心配置
 * - 光斑检测阈值参数
 * - 光斑处理类型设置
 * - 兼容性配置参数
 *
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在photonSensor模块中定义和管理
 * - 专门用于光斑检测和处理
 * - 支持多通道和单点模式
 */
class FaculaConfigData : public Config::BaseConfigData<FaculaConfigData> {
  public:
    FaculaConfigData();
    ~FaculaConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() {
        return "Facula";
    }

    // IConfigData接口实现
    QString getTypeName() const override {
        return staticTypeName();
    }
    QString getVersion() const override {
        return "1.0.0";
    }
    QString getDescription() const override {
        return "光斑检测配置，包含多通道光斑中心和处理参数";
    }

    QVariantMap toVariantMap() const override;
    bool        fromVariantMap(const QVariantMap &data) override;
    bool        validate() const override;
    void        setDefaults() override;

    QStringList getFieldNames() const override;
    QString     getFieldType(const QString &fieldName) const override;
    QString     getFieldDescription(const QString &fieldName) const override;
    bool        hasField(const QString &fieldName) const override;
    QVariant    getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool        setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool        resetField(const QString &fieldName) override;

    // 多通道配置访问接口
    /**
     * @brief 获取多通道配置字符串
     * @return 多通道配置字符串 (格式: "x1,y1;x2,y2;...")
     */
    const QString &getFaculaCenterChannels() const {
        return facula_center_channels;
    }

    /**
     * @brief 设置多通道配置字符串
     * @param channels 多通道配置字符串
     */
    void setFaculaCenterChannels(const QString &channels);

    /**
     * @brief 获取解析后的通道坐标列表
     * @return 通道坐标列表
     */
    const QVector<QPoint> &getFaculaCenterPoints() const {
        return facula_center_points;
    }

    /**
     * @brief 设置通道坐标列表
     * @param points 通道坐标列表
     */
    void setFaculaCenterPoints(const QVector<QPoint> &points);

    /**
     * @brief 获取多通道模式下的peak阈值
     * @return peak阈值
     */
    uint32_t getFaculaCenterPeakThreshold() const {
        return facula_center_peak_threshold;
    }

    /**
     * @brief 设置多通道模式下的peak阈值
     * @param threshold peak阈值 (100-2000)
     */
    void setFaculaCenterPeakThreshold(uint32_t threshold) {
        facula_center_peak_threshold = threshold;
    }

    // 兼容性配置访问接口
    /**
     * @brief 获取单点X坐标
     * @return 单点X坐标
     */
    uint8_t getFaculaCenterLocX() const {
        return facula_center_loc_x;
    }

    /**
     * @brief 设置单点X坐标
     * @param x 单点X坐标 (0-255)
     */
    void setFaculaCenterLocX(uint8_t x) {
        facula_center_loc_x = x;
    }

    /**
     * @brief 获取单点Y坐标
     * @return 单点Y坐标
     */
    uint8_t getFaculaCenterLocY() const {
        return facula_center_loc_y;
    }

    /**
     * @brief 设置单点Y坐标
     * @param y 单点Y坐标 (0-255)
     */
    void setFaculaCenterLocY(uint8_t y) {
        facula_center_loc_y = y;
    }

    // 光斑处理类型访问接口
    /**
     * @brief 获取光斑处理类型
     * @return 处理类型：0-基础，1-增强，2-高精度
     */
    uint8_t getFaculaHandleType() const {
        return facula_handle_type;
    }

    /**
     * @brief 设置光斑处理类型
     * @param type 处理类型：0-基础，1-增强，2-高精度
     */
    void setFaculaHandleType(uint8_t type) {
        facula_handle_type = type;
    }

    /**
     * @brief 获取光斑处理类型的描述
     * @return 处理类型描述
     */
    QString getFaculaHandleTypeDescription() const;

    // 便利方法
    /**
     * @brief 解析通道配置字符串为坐标列表
     * @param channels 通道配置字符串
     * @return 解析后的坐标列表
     */
    static QVector<QPoint> parseChannelString(const QString &channels);

    /**
     * @brief 将坐标列表转换为通道配置字符串
     * @param points 坐标列表
     * @return 通道配置字符串
     */
    static QString pointsToChannelString(const QVector<QPoint> &points);

    /**
     * @brief 添加通道点
     * @param point 通道坐标
     */
    void addChannelPoint(const QPoint &point);

    /**
     * @brief 移除通道点
     * @param index 通道索引
     * @return 是否成功移除
     */
    bool removeChannelPoint(int index);

    /**
     * @brief 清空所有通道点
     */
    void clearChannelPoints();

    /**
     * @brief 获取通道数量
     * @return 通道数量
     */
    int getChannelCount() const {
        return facula_center_points.size();
    }

    /**
     * @brief 检查配置是否为默认值
     * @return 是否为默认值
     */
    bool isDefaultConfig() const;

    /**
     * @brief 获取配置摘要信息
     * @return 配置摘要字符串
     */
    QString getConfigSummary() const;

    // ========== 新增XML参数访问接口 ==========

    // Motion Control Parameters - 运动控制参数
    int getInitialXDist() const {
        return initial_x_dist;
    }
    int getInitialYDist() const {
        return initial_y_dist;
    }
    int getInitialZDist() const {
        return initial_z_dist;
    }
    int getDefaultZDirect() const {
        return default_z_direct;
    }
    int getZMoveStep() const {
        return z_move_step;
    }

    void setInitialXDist(int value) {
        initial_x_dist = value;
    }
    void setInitialYDist(int value) {
        initial_y_dist = value;
    }
    void setInitialZDist(int value) {
        initial_z_dist = value;
    }
    void setDefaultZDirect(int value) {
        default_z_direct = value;
    }
    void setZMoveStep(int value) {
        z_move_step = value;
    }

    // Search Parameters - 搜索参数
    int getFindOriginRadius() const {
        return find_origin_raduis;
    }
    int getFindAngleStep() const {
        return find_angle_step;
    }
    int getFindRadiusStep() const {
        return find_radius_step;
    }
    int getFindTimes() const {
        return find_times;
    }
    int getDiscardPackNum() const {
        return discard_pack_num;
    }

    void setFindOriginRadius(int value) {
        find_origin_raduis = value;
    }
    void setFindAngleStep(int value) {
        find_angle_step = value;
    }
    void setFindRadiusStep(int value) {
        find_radius_step = value;
    }
    void setFindTimes(int value) {
        find_times = value;
    }
    void setDiscardPackNum(int value) {
        discard_pack_num = value;
    }

    // Threshold Parameters - 阈值参数
    int getPeakOkThreshold() const {
        return peak_ok_threshold;
    }
    int getPeakThreshold() const {
        return peak_threshold;
    }
    int getPeakMaxThreshold() const {
        return peak_max_threshold;
    }
    int getEdgePeakThreshold() const {
        return edge_peak_threshold;
    }

    void setPeakOkThreshold(int value) {
        peak_ok_threshold = value;
    }
    void setPeakThreshold(int value) {
        peak_threshold = value;
    }
    void setPeakMaxThreshold(int value) {
        peak_max_threshold = value;
    }
    void setEdgePeakThreshold(int value) {
        edge_peak_threshold = value;
    }

    // Symmetry Parameters - 对称性参数
    int getAmpSelect() const {
        return Amp_select;
    }
    int getLRPeakOffset() const {
        return LR_peak_offset;
    }
    int getUDPeakOffset() const {
        return UD_peak_offset;
    }
    int getALRMpPeak() const {
        return ALR_mp_peak;
    }
    int getALRMpPeakThreshold() const {
        return ALR_mp_peak_threshold;
    }
    int getAUDMpPeak() const {
        return AUD_mp_peak;
    }
    int getAUDMpPeakThreshold() const {
        return AUD_mp_peak_threshold;
    }
    int getACRPeakDelta() const {
        return ACR_peak_delta;
    }
    int getCRPeakDelta() const {
        return CR_peak_delta;
    }
    int getARRPeakDelta() const {
        return ARR_peak_delta;
    }
    int getAedgePeakThreshold() const {
        return Aedge_peak_threshold;
    }
    int getAMaxPeak() const {
        return AMax_peak;
    }

    void setAmpSelect(int value) {
        Amp_select = value;
    }
    void setLRPeakOffset(int value) {
        LR_peak_offset = value;
    }
    void setUDPeakOffset(int value) {
        UD_peak_offset = value;
    }
    void setALRMpPeak(int value) {
        ALR_mp_peak = value;
    }
    void setALRMpPeakThreshold(int value) {
        ALR_mp_peak_threshold = value;
    }
    void setAUDMpPeak(int value) {
        AUD_mp_peak = value;
    }
    void setAUDMpPeakThreshold(int value) {
        AUD_mp_peak_threshold = value;
    }
    void setACRPeakDelta(int value) {
        ACR_peak_delta = value;
    }
    void setCRPeakDelta(int value) {
        CR_peak_delta = value;
    }
    void setARRPeakDelta(int value) {
        ARR_peak_delta = value;
    }
    void setAedgePeakThreshold(int value) {
        Aedge_peak_threshold = value;
    }
    void setAMaxPeak(int value) {
        AMax_peak = value;
    }

    // Advanced Parameters - 高级参数
    int getFTAdjustPeakRadioThreshold() const {
        return FT_adjust_peak_radio_threshold;
    }
    int getFTSolidPeakRadioThreshold() const {
        return FT_solid_peak_radio_threshold;
    }
    int getFTDeflatePeakRadioThreshold() const {
        return FT_deflate_peak_radio_threshold;
    }
    int getUaroundPeakThreshold() const {
        return Uaround_peak_threshold;
    }
    int getApeakOffsetRadio() const {
        return Apeak_offset_radio;
    }

    // FT微调参数 - 缺失的8个参数
    int getFTLRmpAdjustPeak() const {
        return FT_LRmp_adjust_peak;
    }
    int getFTLRmpAdjustPeakThreshold() const {
        return FT_LRmp_adjust_peak_threshold;
    }
    int getFTUDmpAdjustPeak() const {
        return FT_UDmp_adjust_peak;
    }
    int getFTUDmpAdjustPeakThreshold() const {
        return FT_UDmp_adjust_peak_threshold;
    }
    int getFTLRmpSolidPeak() const {
        return FT_LRmp_solid_peak;
    }
    int getFTLRmpSolidPeakThreshold() const {
        return FT_LRmp_solid_peak_threshold;
    }
    int getFTUDmpSolidPeak() const {
        return FT_UDmp_solid_peak;
    }
    int getFTUDmpSolidPeakThreshold() const {
        return FT_UDmp_solid_peak_threshold;
    }
    int getFTLRmpDeflatePeak() const {
        return FT_LRmp_deflate_peak;
    }
    int getFTLRmpDeflatePeakThreshold() const {
        return FT_LRmp_deflate_peak_threshold;
    }
    int getFTUDmpDeflatePeak() const {
        return FT_UDmp_deflate_peak;
    }
    int getFTUDmpDeflatePeakThreshold() const {
        return FT_UDmp_deflate_peak_threshold;
    }

    void setFTAdjustPeakRadioThreshold(int value) {
        FT_adjust_peak_radio_threshold = value;
    }
    void setFTSolidPeakRadioThreshold(int value) {
        FT_solid_peak_radio_threshold = value;
    }
    void setFTDeflatePeakRadioThreshold(int value) {
        FT_deflate_peak_radio_threshold = value;
    }
    void setUaroundPeakThreshold(int value) {
        Uaround_peak_threshold = value;
    }
    void setApeakOffsetRadio(int value) {
        Apeak_offset_radio = value;
    }

    // FT微调参数setter方法
    void setFTLRmpAdjustPeak(int value) {
        FT_LRmp_adjust_peak = value;
    }
    void setFTLRmpAdjustPeakThreshold(int value) {
        FT_LRmp_adjust_peak_threshold = value;
    }
    void setFTUDmpAdjustPeak(int value) {
        FT_UDmp_adjust_peak = value;
    }
    void setFTUDmpAdjustPeakThreshold(int value) {
        FT_UDmp_adjust_peak_threshold = value;
    }
    void setFTLRmpSolidPeak(int value) {
        FT_LRmp_solid_peak = value;
    }
    void setFTLRmpSolidPeakThreshold(int value) {
        FT_LRmp_solid_peak_threshold = value;
    }
    void setFTUDmpSolidPeak(int value) {
        FT_UDmp_solid_peak = value;
    }
    void setFTUDmpSolidPeakThreshold(int value) {
        FT_UDmp_solid_peak_threshold = value;
    }
    void setFTLRmpDeflatePeak(int value) {
        FT_LRmp_deflate_peak = value;
    }
    void setFTLRmpDeflatePeakThreshold(int value) {
        FT_LRmp_deflate_peak_threshold = value;
    }
    void setFTUDmpDeflatePeak(int value) {
        FT_UDmp_deflate_peak = value;
    }
    void setFTUDmpDeflatePeakThreshold(int value) {
        FT_UDmp_deflate_peak_threshold = value;
    }

  public:
    // 配置数据成员
    QString         facula_center_channels;        // 多通道配置字符串
    QVector<QPoint> facula_center_points;          // 解析后的通道坐标列表
    uint32_t        facula_center_peak_threshold;  // 多通道模式下的peak阈值

    // 兼容性配置
    uint8_t facula_center_loc_x;  // 单点X坐标
    uint8_t facula_center_loc_y;  // 单点Y坐标

    // 光斑处理类型
    uint8_t facula_handle_type;  // 处理类型：0-基础，1-增强，2-高精度

    // ========== 新增XML参数数据成员 ==========

    // Motion control parameters - 运动控制参数
    int initial_x_dist;    // 初始X移动距离/um
    int initial_y_dist;    // 初始Y移动距离/um
    int initial_z_dist;    // 初始Z移动距离/um
    int default_z_direct;  // 默认Z轴方向
    int z_move_step;       // Z轴移动步进(脉冲数)

    // Search parameters - 搜索参数
    int find_origin_raduis;  // 初始寻找光斑半径/um
    int find_angle_step;     // 寻找光斑角度步进/°
    int find_radius_step;    // 寻找光斑半径步进/um
    int find_times;          // 寻找光斑次数
    int discard_pack_num;    // 丢弃package数

    // Threshold parameters - 阈值参数
    int peak_ok_threshold;    // 达到后不再查找继续查找
    int peak_threshold;       // 基础peak阈值
    int peak_max_threshold;   // 最大peak阈值
    int edge_peak_threshold;  // 外圈光斑光强最小阈值

    // Symmetry parameters - 对称性参数
    int Amp_select;             // 十字光斑特定通道调节
    int LR_peak_offset;         // 左右通道偏移
    int UD_peak_offset;         // 上下通道偏移
    int ALR_mp_peak;            // 左右通道peak比例*100
    int ALR_mp_peak_threshold;  // 左右通道容差
    int AUD_mp_peak;            // 上下通道peak比例*100
    int AUD_mp_peak_threshold;  // 上下通道容差
    int ACR_peak_delta;         // 中心与十字光斑光强差值最小阈值
    int CR_peak_delta;          // 中心与周围差值阈值
    int ARR_peak_delta;         // 调节peak差值
    int Aedge_peak_threshold;   // 边缘peak阈值
    int AMax_peak;              // 最大peak限制

    // Advanced parameters - 高级参数
    int FT_adjust_peak_radio_threshold;   // 调节模式peak比例阈值
    int FT_solid_peak_radio_threshold;    // 固化模式peak比例阈值
    int FT_deflate_peak_radio_threshold;  // 收缩模式peak比例阈值
    int Uaround_peak_threshold;           // 周围peak阈值
    int Apeak_offset_radio;               // peak偏移比例

    // FT微调参数 - 缺失的8个参数
    int FT_LRmp_adjust_peak;             // 左右中点调节峰值
    int FT_LRmp_adjust_peak_threshold;   // 左右中点调节峰值阈值
    int FT_UDmp_adjust_peak;             // 上下中点调节峰值
    int FT_UDmp_adjust_peak_threshold;   // 上下中点调节峰值阈值
    int FT_LRmp_solid_peak;              // 左右中点固化峰值
    int FT_LRmp_solid_peak_threshold;    // 左右中点固化峰值阈值
    int FT_UDmp_solid_peak;              // 上下中点固化峰值
    int FT_UDmp_solid_peak_threshold;    // 上下中点固化峰值阈值
    int FT_LRmp_deflate_peak;            // 左右中点收缩峰值
    int FT_LRmp_deflate_peak_threshold;  // 左右中点收缩峰值阈值
    int FT_UDmp_deflate_peak;            // 上下中点收缩峰值
    int FT_UDmp_deflate_peak_threshold;  // 上下中点收缩峰值阈值

  private:
    /**
     * @brief 验证通道配置字符串
     * @param channels 通道配置字符串
     * @return 是否有效
     */
    bool validateChannelString(const QString &channels) const;

    /**
     * @brief 验证peak阈值
     * @param threshold peak阈值
     * @return 是否有效
     */
    bool validatePeakThreshold(uint32_t threshold) const;

    /**
     * @brief 验证处理类型
     * @param type 处理类型
     * @return 是否有效
     */
    bool validateHandleType(uint8_t type) const;

    /**
     * @brief 同步通道字符串和坐标列表
     */
    void syncChannelData();

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;
    static const QStringList            s_handleTypeDescriptions;

    // 参数范围定义
    static const uint32_t MIN_PEAK_THRESHOLD = 100;
    static const uint32_t MAX_PEAK_THRESHOLD = 2000;
    static const uint8_t  MAX_COORDINATE     = 255;
    static const uint8_t  MAX_HANDLE_TYPE    = 2;
};

}  // namespace PhotonSensor

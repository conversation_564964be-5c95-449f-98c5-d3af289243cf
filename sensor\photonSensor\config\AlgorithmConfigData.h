#pragma once

#include "../../../components/configModule/ConfigTypeRegistry.h"
#include "../../../components/configModule/IConfigData.h"
#include <QString>
#include <QVariantMap>

namespace PhotonSensor {

/**
 * @brief 算法配置数据
 *
 * 管理图像处理和算法计算的所有配置参数：
 * - 图像处理参数（插值、滤波等）
 * - 算法计算参数
 * - 性能优化参数
 *
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在photonSensor模块中定义和管理
 * - 专门用于算法和图像处理
 * - 支持多种滤波器和插值算法
 */
class AlgorithmConfigData : public Config::BaseConfigData<AlgorithmConfigData> {
  public:
    AlgorithmConfigData();
    ~AlgorithmConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() {
        return "Algorithm";
    }

    // IConfigData接口实现
    QString getTypeName() const override {
        return staticTypeName();
    }
    QString getVersion() const override {
        return "1.0.0";
    }
    QString getDescription() const override {
        return "算法配置，包含图像处理和算法计算参数";
    }

    QVariantMap toVariantMap() const override;
    bool        fromVariantMap(const QVariantMap &data) override;
    bool        validate() const override;
    void        setDefaults() override;

    QStringList getFieldNames() const override;
    QString     getFieldType(const QString &fieldName) const override;
    QString     getFieldDescription(const QString &fieldName) const override;
    bool        hasField(const QString &fieldName) const override;
    QVariant    getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool        setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool        resetField(const QString &fieldName) override;

    // 图像处理参数访问接口
    /**
     * @brief 获取插值类型
     * @return 插值类型：0-最近邻，1-双线性，2-双三次
     */
    int getInterpolationType() const {
        return interpolation_type;
    }

    /**
     * @brief 设置插值类型
     * @param type 插值类型：0-最近邻，1-双线性，2-双三次
     */
    void setInterpolationType(int type) {
        interpolation_type = type;
    }

    /**
     * @brief 获取滤波器类型
     * @return 滤波器类型组合（位掩码）
     */
    int getFilterTypes() const {
        return filter_types;
    }

    /**
     * @brief 设置滤波器类型
     * @param types 滤波器类型组合（位掩码）
     */
    void setFilterTypes(int types) {
        filter_types = types;
    }

    /**
     * @brief 获取插值偏移量
     * @return 插值偏移量
     */
    float getInterpolationOffset() const {
        return interpolation_offset;
    }

    /**
     * @brief 设置插值偏移量
     * @param offset 插值偏移量
     */
    void setInterpolationOffset(float offset) {
        interpolation_offset = offset;
    }

    /**
     * @brief 获取全局滤波强度
     * @return 全局滤波强度
     */
    float getFilterStrength() const {
        return filter_strength;
    }

    /**
     * @brief 设置全局滤波强度
     * @param strength 全局滤波强度
     */
    void setFilterStrength(float strength) {
        filter_strength = strength;
    }

    /**
     * @brief 获取卡尔曼滤波强度
     * @return 卡尔曼滤波强度
     */
    float getKalmanStrength() const {
        return kalman_strength;
    }

    /**
     * @brief 设置卡尔曼滤波强度
     * @param strength 卡尔曼滤波强度
     */
    void setKalmanStrength(float strength) {
        kalman_strength = strength;
    }

    // 卷积滤波器参数
    int getConvolutionKernelSize() const {
        return convolution_kernel_size;
    }
    void setConvolutionKernelSize(int size) {
        convolution_kernel_size = size;
    }

    const QString &getConvolutionPreset() const {
        return convolution_preset;
    }
    void setConvolutionPreset(const QString &preset) {
        convolution_preset = preset;
    }

    // 中值滤波器参数
    int getMedianKernelSize() const {
        return median_kernel_size;
    }
    void setMedianKernelSize(int size) {
        median_kernel_size = size;
    }

    const QString &getMedianPreset() const {
        return median_preset;
    }
    void setMedianPreset(const QString &preset) {
        median_preset = preset;
    }

    // 高斯滤波器参数
    float getGaussianSigma() const {
        return gaussian_sigma;
    }
    void setGaussianSigma(float sigma) {
        gaussian_sigma = sigma;
    }

    int getGaussianKernelSize() const {
        return gaussian_kernel_size;
    }
    void setGaussianKernelSize(int size) {
        gaussian_kernel_size = size;
    }

    const QString &getGaussianPreset() const {
        return gaussian_preset;
    }
    void setGaussianPreset(const QString &preset) {
        gaussian_preset = preset;
    }

    // 双边滤波器参数
    float getBilateralSigmaColor() const {
        return bilateral_sigma_color;
    }
    void setBilateralSigmaColor(float sigma) {
        bilateral_sigma_color = sigma;
    }

    float getBilateralSigmaSpace() const {
        return bilateral_sigma_space;
    }
    void setBilateralSigmaSpace(float sigma) {
        bilateral_sigma_space = sigma;
    }

    int getBilateralKernelSize() const {
        return bilateral_kernel_size;
    }
    void setBilateralKernelSize(int size) {
        bilateral_kernel_size = size;
    }

    const QString &getBilateralPreset() const {
        return bilateral_preset;
    }
    void setBilateralPreset(const QString &preset) {
        bilateral_preset = preset;
    }

    // 加权均值滤波器参数
    int getWeightedAvgKernelSize() const {
        return weighted_avg_kernel_size;
    }
    void setWeightedAvgKernelSize(int size) {
        weighted_avg_kernel_size = size;
    }

    const QString &getWeightedAvgPreset() const {
        return weighted_avg_preset;
    }
    void setWeightedAvgPreset(const QString &preset) {
        weighted_avg_preset = preset;
    }

  private:
    // 图像处理参数
    int     interpolation_type;    // 插值类型
    int     filter_types;          // 滤波器类型组合
    float   interpolation_offset;  // 插值偏移量
    float   filter_strength;       // 全局滤波强度
    float   kalman_strength;       // 卡尔曼滤波强度

    // 卷积滤波器参数
    int     convolution_kernel_size;  // 卷积核大小
    QString convolution_preset;       // 卷积预设

    // 中值滤波器参数
    int     median_kernel_size;  // 中值滤波核大小
    QString median_preset;       // 中值滤波预设

    // 高斯滤波器参数
    float   gaussian_sigma;        // 高斯滤波标准差
    int     gaussian_kernel_size;  // 高斯滤波核大小
    QString gaussian_preset;       // 高斯滤波预设

    // 双边滤波器参数
    float   bilateral_sigma_color;  // 双边滤波颜色标准差
    float   bilateral_sigma_space;  // 双边滤波空间标准差
    int     bilateral_kernel_size;  // 双边滤波核大小
    QString bilateral_preset;       // 双边滤波预设

    // 加权均值滤波器参数
    int     weighted_avg_kernel_size;  // 加权均值滤波核大小
    QString weighted_avg_preset;       // 加权均值滤波预设

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;
};

}  // namespace PhotonSensor
